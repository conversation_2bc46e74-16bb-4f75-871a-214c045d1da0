using System.ComponentModel;

namespace AirMonitor.Models;

/// <summary>
/// 帧数据项，用于在监听窗体中显示
/// </summary>
public class FrameDataItem : INotifyPropertyChanged
{
    private DateTime _timestamp;
    private string _hexData = string.Empty;
    private string _protocolType = string.Empty;
    private bool _isCrcValid;
    private string _portName = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp
    {
        get => _timestamp;
        set
        {
            if (_timestamp != value)
            {
                _timestamp = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TimestampString));
            }
        }
    }

    /// <summary>
    /// 格式化的时间戳字符串
    /// </summary>
    public string TimestampString => Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");

    /// <summary>
    /// 16进制数据字符串
    /// </summary>
    public string HexData
    {
        get => _hexData;
        set
        {
            if (_hexData != value)
            {
                _hexData = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 协议类型
    /// </summary>
    public string ProtocolType
    {
        get => _protocolType;
        set
        {
            if (_protocolType != value)
            {
                _protocolType = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// CRC校验是否通过
    /// </summary>
    public bool IsCrcValid
    {
        get => _isCrcValid;
        set
        {
            if (_isCrcValid != value)
            {
                _isCrcValid = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CrcStatusText));
            }
        }
    }

    /// <summary>
    /// CRC状态文本
    /// </summary>
    public string CrcStatusText => IsCrcValid ? "通过" : "失败";

    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName
    {
        get => _portName;
        set
        {
            if (_portName != value)
            {
                _portName = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 完整显示文本（用于复制和导出）
    /// </summary>
    public string FullText => $"[{TimestampString}] [{ProtocolType}] [{PortName}] [{CrcStatusText}] {HexData}";

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 从数据包创建帧数据项
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <param name="portName">端口名称</param>
    /// <returns>帧数据项</returns>
    public static FrameDataItem FromDataPacket(DataPacketBase packet, string portName = "")
    {
        var hexData = string.Join(" ", packet.RawData.Select(b => b.ToString("X2")));
        var protocolType = packet.ProtocolType switch
        {
            DataPacketProtocolType.CommercialProtocol => "商用协议",
            DataPacketProtocolType.ModuleProtocol => "模块协议",
            _ => "未知协议"
        };

        return new FrameDataItem
        {
            Timestamp = packet.ReceivedTime,
            HexData = hexData,
            ProtocolType = protocolType,
            IsCrcValid = packet.IsCrcValid,
            PortName = portName
        };
    }
}
