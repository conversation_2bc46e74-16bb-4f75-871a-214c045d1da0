using System.Windows;
using AirMonitor.ViewModels;

namespace AirMonitor.Views;

/// <summary>
/// 协议数据解析配置窗口
/// </summary>
public partial class ProtocolParseConfigWindow : Window
{
    public ProtocolParseConfigWindow()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 如果DataContext是ProtocolParseConfigViewModel，则初始化它
        if (DataContext is ProtocolParseConfigViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}
