<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  主窗口样式  -->
    <Style x:Key="MainWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="#F5F5F5" />
        <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
        <Setter Property="FontSize" Value="12" />
    </Style>

    <!--  菜单栏样式  -->
    <Style x:Key="MainMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Padding" Value="5,2" />
    </Style>

    <!--  菜单项样式  -->
    <Style x:Key="MainMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Margin" Value="2,0" />
    </Style>

    <!--  工具栏样式  -->
    <Style x:Key="MainToolBarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Padding" Value="5,3" />
    </Style>

    <!--  工具栏托盘样式  -->
    <Style x:Key="MainToolBarTrayStyle" TargetType="ToolBarTray">
        <Setter Property="Background" Value="White" />
    </Style>

    <!--  状态栏样式  -->
    <Style x:Key="MainStatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,1,0,0" />
        <Setter Property="Padding" Value="5,3" />
        <Setter Property="Height" Value="24" />
    </Style>

    <!--  主内容区域边框样式  -->
    <Style x:Key="MainContentBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="#D0D0D0" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="3" />
        <Setter Property="Margin" Value="8" />
        <Setter Property="Padding" Value="10" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect
                    BlurRadius="3"
                    Direction="270"
                    Opacity="0.1"
                    ShadowDepth="1" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  占位符文本样式  -->
    <Style x:Key="PlaceholderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Foreground" Value="#888888" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="LineHeight" Value="20" />
    </Style>

    <!--  响应式网格样式  -->
    <Style x:Key="ResponsiveGridStyle" TargetType="Grid">
        <Setter Property="Margin" Value="0" />
    </Style>

    <!--  自适应内容容器样式  -->
    <Style x:Key="AdaptiveContentStyle" TargetType="ContentPresenter">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
    </Style>

    <!--  状态栏项样式  -->
    <Style x:Key="StatusBarItemStyle" TargetType="StatusBarItem">
        <Setter Property="Padding" Value="5,2" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <!--  状态栏文本样式  -->
    <Style x:Key="StatusBarTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="11" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

</ResourceDictionary>
