using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AirMonitor.Models;

/// <summary>
/// 物理量定义
/// </summary>
public class PhysicalQuantity
{
    /// <summary>
    /// 物理量名称（唯一标识）
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "物理量名称长度必须在1-100字符之间")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 物理量显示名称
    /// </summary>
    [Required]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "显示名称长度必须在1-200字符之间")]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 物理量描述
    /// </summary>
    [StringLength(500, ErrorMessage = "描述长度不能超过500字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 数据类型
    /// </summary>
    [Required]
    public DataValueType DataType { get; set; } = DataValueType.UInt8;

    /// <summary>
    /// 单位
    /// </summary>
    [StringLength(50, ErrorMessage = "单位长度不能超过50字符")]
    public string Unit { get; set; } = string.Empty;

    /// <summary>
    /// 缩放因子（原始值 * 缩放因子 = 实际值）
    /// </summary>
    public double ScaleFactor { get; set; } = 1.0;

    /// <summary>
    /// 偏移量（(原始值 * 缩放因子) + 偏移量 = 实际值）
    /// </summary>
    public double Offset { get; set; } = 0.0;

    /// <summary>
    /// 最小有效值
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// 最大有效值
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// 小数位数（用于显示格式化）
    /// </summary>
    [Range(0, 10, ErrorMessage = "小数位数必须在0-10之间")]
    public int DecimalPlaces { get; set; } = 2;

    /// <summary>
    /// 是否为只读物理量
    /// </summary>
    public bool IsReadOnly { get; set; } = true;

    /// <summary>
    /// 分组名称（用于界面分组显示）
    /// </summary>
    [StringLength(100, ErrorMessage = "分组名称长度不能超过100字符")]
    public string GroupName { get; set; } = "默认分组";

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000字符")]
    public string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// 计算实际值
    /// </summary>
    /// <param name="rawValue">原始值</param>
    /// <returns>实际值</returns>
    public double CalculateActualValue(double rawValue)
    {
        return (rawValue * ScaleFactor) + Offset;
    }

    /// <summary>
    /// 验证值是否在有效范围内
    /// </summary>
    /// <param name="value">要验证的值</param>
    /// <returns>是否有效</returns>
    public bool IsValueValid(double value)
    {
        if (MinValue.HasValue && value < MinValue.Value)
            return false;

        if (MaxValue.HasValue && value > MaxValue.Value)
            return false;

        return true;
    }

    /// <summary>
    /// 格式化显示值
    /// </summary>
    /// <param name="value">值</param>
    /// <returns>格式化后的字符串</returns>
    public string FormatValue(double value)
    {
        var formatString = DecimalPlaces > 0 ? $"F{DecimalPlaces}" : "F0";
        var formattedValue = value.ToString(formatString);
        
        return string.IsNullOrEmpty(Unit) ? formattedValue : $"{formattedValue} {Unit}";
    }

    public override string ToString()
    {
        return $"{DisplayName} ({Name})";
    }
}

/// <summary>
/// 物理量值
/// </summary>
public class PhysicalQuantityValue
{
    /// <summary>
    /// 物理量名称
    /// </summary>
    [Required]
    public string PhysicalQuantityName { get; set; } = string.Empty;

    /// <summary>
    /// 原始值
    /// </summary>
    public object? RawValue { get; set; }

    /// <summary>
    /// 实际值
    /// </summary>
    public double ActualValue { get; set; }

    /// <summary>
    /// 格式化显示值
    /// </summary>
    public string FormattedValue { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 数据源信息
    /// </summary>
    public string SourceInfo { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息（如果解析失败）
    /// </summary>
    public string? ErrorMessage { get; set; }

    public override string ToString()
    {
        return $"{PhysicalQuantityName}: {FormattedValue}";
    }
}

/// <summary>
/// 物理量分组
/// </summary>
public class PhysicalQuantityGroup
{
    /// <summary>
    /// 分组名称
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "分组名称长度必须在1-100字符之间")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 分组显示名称
    /// </summary>
    [Required]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "显示名称长度必须在1-200字符之间")]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 分组描述
    /// </summary>
    [StringLength(500, ErrorMessage = "描述长度不能超过500字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 分组中的物理量列表
    /// </summary>
    public List<PhysicalQuantity> PhysicalQuantities { get; set; } = new();

    public override string ToString()
    {
        return $"{DisplayName} ({PhysicalQuantities.Count} 项)";
    }
}
