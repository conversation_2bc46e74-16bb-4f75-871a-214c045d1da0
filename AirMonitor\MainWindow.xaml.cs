﻿using System.Windows;
using AirMonitor.ViewModels;

namespace AirMonitor;

/// <summary>
/// 主窗口
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 如果DataContext是MainViewModel，则初始化它
        if (DataContext is MainViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}