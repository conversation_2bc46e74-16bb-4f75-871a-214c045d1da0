using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using AirMonitor.Models;

namespace AirMonitor.Controls;

/// <summary>
/// 字节位置选择器用户控件
/// </summary>
public partial class BytePositionSelector : UserControl
{
    #region 依赖属性

    /// <summary>
    /// 字节索引依赖属性
    /// </summary>
    public static readonly DependencyProperty ByteIndexProperty =
        DependencyProperty.Register(nameof(ByteIndex), typeof(int), typeof(BytePositionSelector),
            new PropertyMetadata(0, OnByteIndexChanged));

    /// <summary>
    /// 位索引依赖属性
    /// </summary>
    public static readonly DependencyProperty BitIndexProperty =
        DependencyProperty.Register(nameof(BitIndex), typeof(int), typeof(BytePositionSelector),
            new PropertyMetadata(0, OnBitIndexChanged));

    /// <summary>
    /// 位长度依赖属性
    /// </summary>
    public static readonly DependencyProperty BitLengthProperty =
        DependencyProperty.Register(nameof(BitLength), typeof(int), typeof(BytePositionSelector),
            new PropertyMetadata(1, OnBitLengthChanged));

    /// <summary>
    /// 目标位偏移依赖属性
    /// </summary>
    public static readonly DependencyProperty TargetBitOffsetProperty =
        DependencyProperty.Register(nameof(TargetBitOffset), typeof(int), typeof(BytePositionSelector),
            new PropertyMetadata(0));

    /// <summary>
    /// 位位置对象依赖属性
    /// </summary>
    public static readonly DependencyProperty BitPositionProperty =
        DependencyProperty.Register(nameof(BitPosition), typeof(BitPosition), typeof(BytePositionSelector),
            new PropertyMetadata(null, OnBitPositionChanged));

    #endregion

    #region 属性

    /// <summary>
    /// 字节索引
    /// </summary>
    public int ByteIndex
    {
        get => (int)GetValue(ByteIndexProperty);
        set => SetValue(ByteIndexProperty, value);
    }

    /// <summary>
    /// 位索引
    /// </summary>
    public int BitIndex
    {
        get => (int)GetValue(BitIndexProperty);
        set => SetValue(BitIndexProperty, value);
    }

    /// <summary>
    /// 位长度
    /// </summary>
    public int BitLength
    {
        get => (int)GetValue(BitLengthProperty);
        set => SetValue(BitLengthProperty, value);
    }

    /// <summary>
    /// 目标位偏移
    /// </summary>
    public int TargetBitOffset
    {
        get => (int)GetValue(TargetBitOffsetProperty);
        set => SetValue(TargetBitOffsetProperty, value);
    }

    /// <summary>
    /// 位位置对象
    /// </summary>
    public BitPosition? BitPosition
    {
        get => (BitPosition?)GetValue(BitPositionProperty);
        set => SetValue(BitPositionProperty, value);
    }

    #endregion

    #region 字段

    private readonly ToggleButton[] _bitButtons;
    private bool _isUpdatingFromBitPosition = false;

    #endregion

    #region 构造函数

    public BytePositionSelector()
    {
        InitializeComponent();
        
        // 初始化位按钮数组
        _bitButtons = new ToggleButton[]
        {
            Bit0, Bit1, Bit2, Bit3, Bit4, Bit5, Bit6, Bit7
        };

        // 设置数据上下文为自己，以便绑定工作
        DataContext = this;
        
        // 初始化位位置对象
        if (BitPosition == null)
        {
            BitPosition = new BitPosition();
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 位按钮点击事件处理
    /// </summary>
    private void OnBitButtonClick(object sender, RoutedEventArgs e)
    {
        if (_isUpdatingFromBitPosition) return;

        if (sender is ToggleButton button && button.Tag is string tagStr && int.TryParse(tagStr, out int bitIndex))
        {
            UpdateBitSelection(bitIndex, button.IsChecked == true);
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 更新位选择状态
    /// </summary>
    private void UpdateBitSelection(int clickedBitIndex, bool isSelected)
    {
        if (isSelected)
        {
            // 选中位，更新起始位索引
            BitIndex = clickedBitIndex;
            
            // 根据位长度更新其他位的选中状态
            for (int i = 0; i < 8; i++)
            {
                bool shouldBeSelected = i >= clickedBitIndex && i < clickedBitIndex + BitLength;
                _bitButtons[i].IsChecked = shouldBeSelected;
                _bitButtons[i].Content = shouldBeSelected ? "1" : "0";
            }
        }
        else
        {
            // 取消选中，清除所有选择
            BitIndex = 0;
            for (int i = 0; i < 8; i++)
            {
                _bitButtons[i].IsChecked = false;
                _bitButtons[i].Content = "0";
            }
        }

        UpdateBitPositionObject();
    }

    /// <summary>
    /// 更新位位置对象
    /// </summary>
    private void UpdateBitPositionObject()
    {
        if (_isUpdatingFromBitPosition) return;

        if (BitPosition == null)
        {
            BitPosition = new BitPosition();
        }

        BitPosition.ByteIndex = ByteIndex;
        BitPosition.BitIndex = BitIndex;
        BitPosition.BitLength = BitLength;
        BitPosition.TargetBitOffset = TargetBitOffset;
    }

    /// <summary>
    /// 从位位置对象更新界面
    /// </summary>
    private void UpdateFromBitPosition()
    {
        if (BitPosition == null) return;

        _isUpdatingFromBitPosition = true;
        try
        {
            ByteIndex = BitPosition.ByteIndex;
            BitIndex = BitPosition.BitIndex;
            BitLength = BitPosition.BitLength;
            TargetBitOffset = BitPosition.TargetBitOffset;

            // 更新位按钮状态
            for (int i = 0; i < 8; i++)
            {
                bool shouldBeSelected = i >= BitPosition.BitIndex && i < BitPosition.BitIndex + BitPosition.BitLength;
                _bitButtons[i].IsChecked = shouldBeSelected;
                _bitButtons[i].Content = shouldBeSelected ? "1" : "0";
            }
        }
        finally
        {
            _isUpdatingFromBitPosition = false;
        }
    }

    #endregion

    #region 依赖属性变更处理

    private static void OnByteIndexChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is BytePositionSelector control)
        {
            control.UpdateBitPositionObject();
        }
    }

    private static void OnBitIndexChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is BytePositionSelector control)
        {
            control.UpdateBitPositionObject();
        }
    }

    private static void OnBitLengthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is BytePositionSelector control)
        {
            // 限制位长度在1-8之间
            int newLength = Math.Max(1, Math.Min(8, (int)e.NewValue));
            if (newLength != (int)e.NewValue)
            {
                control.BitLength = newLength;
                return;
            }

            // 重新计算位选择
            int startBit = control.BitIndex;
            for (int i = 0; i < 8; i++)
            {
                bool shouldBeSelected = i >= startBit && i < startBit + newLength;
                control._bitButtons[i].IsChecked = shouldBeSelected;
                control._bitButtons[i].Content = shouldBeSelected ? "1" : "0";
            }

            control.UpdateBitPositionObject();
        }
    }

    private static void OnBitPositionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is BytePositionSelector control)
        {
            control.UpdateFromBitPosition();
        }
    }

    #endregion
}
