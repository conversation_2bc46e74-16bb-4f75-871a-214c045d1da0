using System.IO;
using Serilog;
using Serilog.Events;
using AirMonitor.Models;

namespace AirMonitor.Helpers;

/// <summary>
/// 日志配置帮助类
/// </summary>
public static class LoggingHelper
{
    /// <summary>
    /// 配置Serilog日志
    /// </summary>
    /// <param name="loggingSettings">日志配置</param>
    public static void ConfigureSerilog(LoggingSettings loggingSettings)
    {
        // 确保日志目录存在
        var logDirectory = Path.GetDirectoryName(loggingSettings.LogFilePath);
        if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }

        // 解析日志级别
        var logLevel = ParseLogLevel(loggingSettings.LogLevel);

        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Is(logLevel)
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithThreadId()
            .Enrich.WithMachineName()
            .WriteTo.File(
                path: loggingSettings.LogFilePath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: loggingSettings.RetainedFileCountLimit,
                fileSizeLimitBytes: loggingSettings.FileSizeLimitMB * 1024 * 1024,
                rollOnFileSizeLimit: true,
                shared: true,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{ThreadId}] {SourceContext}: {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        Log.Information("日志系统初始化完成，日志级别: {LogLevel}，日志文件: {LogFilePath}", 
            logLevel, loggingSettings.LogFilePath);
    }

    /// <summary>
    /// 解析日志级别
    /// </summary>
    /// <param name="logLevel">日志级别字符串</param>
    /// <returns>Serilog日志级别</returns>
    private static LogEventLevel ParseLogLevel(string logLevel)
    {
        return logLevel?.ToLowerInvariant() switch
        {
            "verbose" => LogEventLevel.Verbose,
            "debug" => LogEventLevel.Debug,
            "information" => LogEventLevel.Information,
            "warning" => LogEventLevel.Warning,
            "error" => LogEventLevel.Error,
            "fatal" => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }

    /// <summary>
    /// 关闭并刷新日志
    /// </summary>
    public static void CloseAndFlush()
    {
        Log.CloseAndFlush();
    }

    /// <summary>
    /// 记录应用程序启动信息
    /// </summary>
    /// <param name="applicationName">应用程序名称</param>
    /// <param name="version">版本</param>
    public static void LogApplicationStart(string applicationName, string version)
    {
        Log.Information("=== {ApplicationName} v{Version} 启动 ===", applicationName, version);
        Log.Information("操作系统: {OS}", Environment.OSVersion);
        Log.Information("运行时版本: {RuntimeVersion}", Environment.Version);
        Log.Information("工作目录: {WorkingDirectory}", Environment.CurrentDirectory);
        Log.Information("用户名: {UserName}", Environment.UserName);
        Log.Information("机器名: {MachineName}", Environment.MachineName);
    }

    /// <summary>
    /// 记录应用程序关闭信息
    /// </summary>
    /// <param name="applicationName">应用程序名称</param>
    public static void LogApplicationShutdown(string applicationName)
    {
        Log.Information("=== {ApplicationName} 正常关闭 ===", applicationName);
    }

    /// <summary>
    /// 记录未处理的异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文信息</param>
    public static void LogUnhandledException(Exception exception, string context = "")
    {
        Log.Fatal(exception, "未处理的异常 - {Context}", context);
    }
}
