using AirMonitor.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AirMonitor.Services;

/// <summary>
/// 数据解析服务实现
/// </summary>
public class DataParseService : IDataParseService, IDisposable
{
    #region 字段

    private readonly ILogger<DataParseService> _logger;
    private readonly IProtocolParseConfigService _configService;
    private readonly object _configCacheLock = new();
    
    private Dictionary<DataPacketProtocolType, List<ProtocolParseConfig>> _activeConfigsCache = new();
    private bool _disposed = false;

    #endregion

    #region 构造函数

    public DataParseService(
        ILogger<DataParseService> logger,
        IProtocolParseConfigService configService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configService = configService ?? throw new ArgumentNullException(nameof(configService));

        // 订阅配置变更事件
        _configService.ConfigChanged += OnConfigChanged;

        _logger.LogInformation("数据解析服务已初始化");
    }

    #endregion

    #region 事件

    public event EventHandler<DataParseCompletedEventArgs>? DataParseCompleted;
    public event EventHandler<DataParseErrorEventArgs>? DataParseError;

    #endregion

    #region 数据解析

    public async Task<List<PhysicalQuantityValue>> ParseDataPacketAsync(DataPacketBase packet)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new List<PhysicalQuantityValue>();

        try
        {
            var configs = await GetActiveParseConfigsAsync(packet.ProtocolType);
            
            foreach (var config in configs)
            {
                // 如果配置指定了命令码，检查是否匹配
                if (config.CommandCode.HasValue)
                {
                    byte packetCommandCode = GetCommandCodeFromPacket(packet);
                    if (packetCommandCode != config.CommandCode.Value)
                        continue;
                }

                var parsedValues = await ParseWithConfigAsync(packet, config);
                result.AddRange(parsedValues);
            }

            stopwatch.Stop();

            // 触发解析完成事件
            DataParseCompleted?.Invoke(this, new DataParseCompletedEventArgs
            {
                Packet = packet,
                PhysicalQuantities = result,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
            });

            _logger.LogDebug("数据包解析完成: 协议={Protocol}, 物理量数量={Count}, 耗时={ElapsedMs}ms",
                packet.ProtocolType, result.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            var errorMessage = $"解析数据包时发生错误: {ex.Message}";
            _logger.LogError(ex, errorMessage);

            // 触发解析错误事件
            DataParseError?.Invoke(this, new DataParseErrorEventArgs
            {
                Packet = packet,
                ErrorMessage = errorMessage,
                Exception = ex
            });

            return result;
        }
    }

    public async Task<List<PhysicalQuantityValue>> ParseDataPacketWithConfigAsync(DataPacketBase packet, string configId)
    {
        try
        {
            var config = await _configService.GetConfigByIdAsync(configId);
            if (config == null)
            {
                _logger.LogWarning("指定的配置不存在: {ConfigId}", configId);
                return new List<PhysicalQuantityValue>();
            }

            return await ParseWithConfigAsync(packet, config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用指定配置解析数据包时发生错误: {ConfigId}", configId);
            return new List<PhysicalQuantityValue>();
        }
    }

    public async Task<List<PhysicalQuantityValue>> ParseRawDataAsync(byte[] rawData, DataPacketProtocolType protocolType, byte? commandCode = null)
    {
        try
        {
            var configs = await GetActiveParseConfigsAsync(protocolType);
            var result = new List<PhysicalQuantityValue>();

            foreach (var config in configs)
            {
                if (commandCode.HasValue && config.CommandCode.HasValue && config.CommandCode.Value != commandCode.Value)
                    continue;

                var parsedValues = await ParseRawDataWithConfigAsync(rawData, config);
                result.AddRange(parsedValues);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析原始数据时发生错误");
            return new List<PhysicalQuantityValue>();
        }
    }

    public async Task<PhysicalQuantityValue?> ParseDataWithMappingAsync(byte[] rawData, DataMappingConfig mappingConfig, PhysicalQuantity physicalQuantity)
    {
        try
        {
            object? rawValue = null;

            switch (mappingConfig.MappingType)
            {
                case DataMappingType.ByteMapping:
                    if (mappingConfig.ByteRange != null)
                    {
                        rawValue = ExtractByteRangeValue(rawData, mappingConfig.ByteRange, physicalQuantity.DataType);
                    }
                    break;

                case DataMappingType.BitMapping:
                    if (mappingConfig.BitPositions.Count > 0)
                    {
                        rawValue = ExtractBitValue(rawData, mappingConfig.BitPositions[0]);
                    }
                    break;

                case DataMappingType.CrossByteBitMapping:
                    if (mappingConfig.BitPositions.Count > 1)
                    {
                        rawValue = ExtractCrossByteBitValue(rawData, mappingConfig.BitPositions, physicalQuantity.DataType);
                    }
                    break;

                case DataMappingType.IndexMapping:
                    // 索引映射需要特殊处理，这里暂时跳过
                    _logger.LogDebug("索引映射暂未实现");
                    break;
            }

            if (rawValue != null)
            {
                var actualValue = physicalQuantity.CalculateActualValue(Convert.ToDouble(rawValue));
                var isValid = physicalQuantity.IsValueValid(actualValue);

                return new PhysicalQuantityValue
                {
                    PhysicalQuantityName = physicalQuantity.Name,
                    RawValue = rawValue,
                    ActualValue = actualValue,
                    FormattedValue = physicalQuantity.FormatValue(actualValue),
                    IsValid = isValid,
                    Timestamp = DateTime.Now,
                    SourceInfo = $"映射: {mappingConfig.Name}"
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用映射配置解析数据时发生错误: {MappingName}", mappingConfig.Name);
            
            return new PhysicalQuantityValue
            {
                PhysicalQuantityName = physicalQuantity.Name,
                IsValid = false,
                ErrorMessage = ex.Message,
                Timestamp = DateTime.Now,
                SourceInfo = $"映射: {mappingConfig.Name}"
            };
        }
    }

    #endregion

    #region 索引数据解析

    public async Task<IndexDataParseResult> ParseIndexDataAsync(DataPacketBase packet, IndexParseConfig indexParseConfig)
    {
        var result = new IndexDataParseResult();

        try
        {
            var dataBytes = packet.GetDataBytes();
            
            // 提取索引值
            if (indexParseConfig.IndexByteRange != null)
            {
                var indexValue = ExtractByteRangeValue(dataBytes, indexParseConfig.IndexByteRange, DataValueType.UInt16);
                if (indexValue != null)
                {
                    result.IndexValue = Convert.ToInt32(indexValue);
                }
            }

            // 提取索引数量（如果配置了）
            if (indexParseConfig.IndexCountByteRange != null)
            {
                var indexCount = ExtractByteRangeValue(dataBytes, indexParseConfig.IndexCountByteRange, DataValueType.UInt16);
                if (indexCount != null)
                {
                    result.IndexCount = Convert.ToInt32(indexCount);
                }
            }

            // 解析索引相关的物理量
            if (indexParseConfig.ParseIndexAsPhysicalQuantity)
            {
                // TODO: 实现索引值本身的物理量解析
            }

            result.IsSuccess = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析索引数据时发生错误");
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    public async Task<List<DataMappingConfig>> GetMappingConfigsByIndexAsync(int indexValue, DataPacketProtocolType protocolType, byte commandCode)
    {
        try
        {
            var configs = await GetActiveParseConfigsAsync(protocolType);
            var result = new List<DataMappingConfig>();

            foreach (var config in configs)
            {
                if (config.CommandCode == commandCode)
                {
                    var indexMappings = config.DataMappings.Where(m => 
                        m.MappingType == DataMappingType.IndexMapping &&
                        m.IndexMapping?.IndexValue == indexValue);
                    
                    result.AddRange(indexMappings);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取索引映射配置时发生错误");
            return new List<DataMappingConfig>();
        }
    }

    #endregion

    #region 数据提取

    public object? ExtractByteRangeValue(byte[] rawData, ByteRange byteRange, DataValueType dataType)
    {
        try
        {
            if (rawData.Length <= byteRange.StartIndex || byteRange.StartIndex < 0)
                return null;

            var endIndex = byteRange.StartIndex + byteRange.Length;
            if (endIndex > rawData.Length)
                return null;

            var bytes = new byte[byteRange.Length];
            Array.Copy(rawData, byteRange.StartIndex, bytes, 0, byteRange.Length);

            return ConvertBytesToValue(bytes, dataType, byteRange.ByteOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取字节范围值时发生错误");
            return null;
        }
    }

    public int ExtractBitValue(byte[] rawData, BitPosition bitPosition)
    {
        try
        {
            if (rawData.Length <= bitPosition.ByteIndex || bitPosition.ByteIndex < 0)
                return 0;

            if (bitPosition.BitIndex < 0 || bitPosition.BitIndex > 7)
                return 0;

            var byteValue = rawData[bitPosition.ByteIndex];
            return (byteValue >> bitPosition.BitIndex) & 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取位值时发生错误");
            return 0;
        }
    }

    public object? ExtractCrossByteBitValue(byte[] rawData, List<BitPosition> bitPositions, DataValueType dataType)
    {
        try
        {
            uint result = 0;

            foreach (var bitPos in bitPositions.OrderBy(bp => bp.TargetBitOffset))
            {
                if (rawData.Length <= bitPos.ByteIndex || bitPos.ByteIndex < 0)
                    continue;

                var byteValue = rawData[bitPos.ByteIndex];
                
                // 提取指定长度的位
                var mask = (1u << bitPos.BitLength) - 1;
                var extractedBits = (uint)((byteValue >> bitPos.BitIndex) & mask);
                
                // 将提取的位放置到目标位置
                result |= extractedBits << bitPos.TargetBitOffset;
            }

            return ConvertToDataType(result, dataType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取跨字节位值时发生错误");
            return null;
        }
    }

    #endregion

    #region 私有方法

    private async Task<List<PhysicalQuantityValue>> ParseWithConfigAsync(DataPacketBase packet, ProtocolParseConfig config)
    {
        var result = new List<PhysicalQuantityValue>();
        var dataBytes = packet.GetDataBytes();

        foreach (var mapping in config.DataMappings.Where(m => m.IsEnabled))
        {
            var physicalQuantity = config.PhysicalQuantities.FirstOrDefault(pq => pq.Name == mapping.PhysicalQuantityName);
            if (physicalQuantity == null)
            {
                _logger.LogWarning("映射 '{MappingName}' 引用的物理量 '{QuantityName}' 不存在", 
                    mapping.Name, mapping.PhysicalQuantityName);
                continue;
            }

            var value = await ParseDataWithMappingAsync(dataBytes, mapping, physicalQuantity);
            if (value != null)
            {
                result.Add(value);
            }
        }

        return result;
    }

    private async Task<List<PhysicalQuantityValue>> ParseRawDataWithConfigAsync(byte[] rawData, ProtocolParseConfig config)
    {
        var result = new List<PhysicalQuantityValue>();

        foreach (var mapping in config.DataMappings.Where(m => m.IsEnabled))
        {
            var physicalQuantity = config.PhysicalQuantities.FirstOrDefault(pq => pq.Name == mapping.PhysicalQuantityName);
            if (physicalQuantity == null)
                continue;

            var value = await ParseDataWithMappingAsync(rawData, mapping, physicalQuantity);
            if (value != null)
            {
                result.Add(value);
            }
        }

        return result;
    }

    private byte GetCommandCodeFromPacket(DataPacketBase packet)
    {
        return packet switch
        {
            CommercialProtocolPacket commercial => commercial.CommandCode,
            ModuleProtocolPacket module => module.CommandCode,
            _ => 0
        };
    }

    private void OnConfigChanged(object? sender, ProtocolParseConfigChangedEventArgs e)
    {
        // 清除配置缓存，强制重新加载
        lock (_configCacheLock)
        {
            _activeConfigsCache.Clear();
        }

        _logger.LogDebug("配置变更，已清除配置缓存: {ConfigName}", e.Config.Name);
    }

    #endregion

    #region 数据转换

    public object? ConvertToDataType(object rawValue, DataValueType dataType)
    {
        try
        {
            return dataType switch
            {
                DataValueType.UInt8 => Convert.ToByte(rawValue),
                DataValueType.Int8 => Convert.ToSByte(rawValue),
                DataValueType.UInt16 => Convert.ToUInt16(rawValue),
                DataValueType.Int16 => Convert.ToInt16(rawValue),
                DataValueType.UInt32 => Convert.ToUInt32(rawValue),
                DataValueType.Int32 => Convert.ToInt32(rawValue),
                DataValueType.Float => Convert.ToSingle(rawValue),
                DataValueType.Double => Convert.ToDouble(rawValue),
                DataValueType.Boolean => Convert.ToBoolean(rawValue),
                DataValueType.String => rawValue.ToString(),
                _ => rawValue
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换数据类型时发生错误: {DataType}", dataType);
            return null;
        }
    }

    public object? ConvertBytesToValue(byte[] bytes, DataValueType dataType, ByteOrder byteOrder = ByteOrder.LittleEndian)
    {
        try
        {
            if (bytes == null || bytes.Length == 0)
                return null;

            // 根据字节序调整字节数组
            if (byteOrder == ByteOrder.BigEndian && BitConverter.IsLittleEndian)
            {
                bytes = bytes.Reverse().ToArray();
            }
            else if (byteOrder == ByteOrder.LittleEndian && !BitConverter.IsLittleEndian)
            {
                bytes = bytes.Reverse().ToArray();
            }

            return dataType switch
            {
                DataValueType.UInt8 => bytes.Length >= 1 ? bytes[0] : (byte)0,
                DataValueType.Int8 => bytes.Length >= 1 ? (sbyte)bytes[0] : (sbyte)0,
                DataValueType.UInt16 => bytes.Length >= 2 ? BitConverter.ToUInt16(bytes, 0) : (ushort)0,
                DataValueType.Int16 => bytes.Length >= 2 ? BitConverter.ToInt16(bytes, 0) : (short)0,
                DataValueType.UInt32 => bytes.Length >= 4 ? BitConverter.ToUInt32(bytes, 0) : 0u,
                DataValueType.Int32 => bytes.Length >= 4 ? BitConverter.ToInt32(bytes, 0) : 0,
                DataValueType.Float => bytes.Length >= 4 ? BitConverter.ToSingle(bytes, 0) : 0f,
                DataValueType.Double => bytes.Length >= 8 ? BitConverter.ToDouble(bytes, 0) : 0.0,
                DataValueType.Boolean => bytes.Length >= 1 && bytes[0] != 0,
                DataValueType.String => System.Text.Encoding.UTF8.GetString(bytes).TrimEnd('\0'),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换字节数组到值时发生错误: {DataType}", dataType);
            return null;
        }
    }

    #endregion

    #region 配置管理

    public async Task<bool> SetActiveParseConfigsAsync(DataPacketProtocolType protocolType, List<string> configIds)
    {
        try
        {
            // 获取所有指定协议类型的配置
            var allConfigs = await _configService.GetConfigsByProtocolTypeAsync(protocolType);

            foreach (var config in allConfigs)
            {
                bool shouldBeActive = configIds.Contains(config.Id);
                if (config.IsEnabled != shouldBeActive)
                {
                    await _configService.SetConfigActiveStatusAsync(config.Id, shouldBeActive);
                }
            }

            // 清除缓存
            lock (_configCacheLock)
            {
                _activeConfigsCache.Remove(protocolType);
            }

            _logger.LogInformation("已更新协议 {ProtocolType} 的活动配置: {ConfigCount} 个配置",
                protocolType, configIds.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置活动解析配置时发生错误");
            return false;
        }
    }

    public async Task<List<ProtocolParseConfig>> GetActiveParseConfigsAsync(DataPacketProtocolType protocolType)
    {
        try
        {
            // 检查缓存
            lock (_configCacheLock)
            {
                if (_activeConfigsCache.TryGetValue(protocolType, out var cachedConfigs))
                {
                    return cachedConfigs;
                }
            }

            // 从配置服务获取活动配置
            var activeConfigs = await _configService.GetActiveConfigsAsync(protocolType);

            // 更新缓存
            lock (_configCacheLock)
            {
                _activeConfigsCache[protocolType] = activeConfigs;
            }

            return activeConfigs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活动解析配置时发生错误");
            return new List<ProtocolParseConfig>();
        }
    }

    public async Task<bool> RefreshParseConfigsAsync()
    {
        try
        {
            // 清除所有缓存
            lock (_configCacheLock)
            {
                _activeConfigsCache.Clear();
            }

            _logger.LogInformation("解析配置缓存已刷新");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新解析配置时发生错误");
            return false;
        }
    }

    #endregion

    #region IDisposable

    public void Dispose()
    {
        if (!_disposed)
        {
            _configService.ConfigChanged -= OnConfigChanged;
            _disposed = true;
            _logger.LogInformation("数据解析服务已释放");
        }
    }

    #endregion
}
