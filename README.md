# AirMonitor - WPF应用程序基础架构

这是一个基于WPF和MVVM模式的应用程序基础架构项目，严格按照WPF项目开发规范构建，提供了完整的企业级应用程序开发基础设施。

## 🚀 功能特性

### 核心功能
- ✅ **依赖注入系统** - 使用 Microsoft.Extensions.DependencyInjection
- ✅ **MVVM架构** - 基于 CommunityToolkit.Mvvm
- ✅ **日志系统** - 集成 Serilog，支持文件日志输出
- ✅ **配置管理** - 基于 Microsoft.Extensions.Configuration，支持JSON配置
- ✅ **视图模型自动绑定** - 实现ViewModelLocator自动绑定机制
- ✅ **全局异常处理** - 完整的异常处理和日志记录

### 架构特点
- 🏗️ **标准项目结构** - 遵循WPF项目开发规范
- 🔧 **松耦合设计** - 基于接口和依赖注入
- 📝 **完整日志记录** - 应用程序生命周期和操作日志
- ⚡ **高性能** - 异步操作和资源管理
- 🛡️ **健壮性** - 异常处理和错误恢复

## 📁 项目结构

```
AirMonitor/
├── Models/                 # 数据模型
│   └── AppSettings.cs     # 应用程序配置模型
├── ViewModels/            # 视图模型
│   ├── ViewModelBase.cs   # ViewModel基类
│   ├── ViewModelLocator.cs # ViewModel定位器
│   ├── MainViewModel.cs   # 主窗口ViewModel
│   └── SampleViewModel.cs # 示例ViewModel
├── Views/                 # 视图
│   └── SampleView.xaml    # 示例用户控件
├── Services/              # 服务层
│   ├── IConfigurationService.cs    # 配置服务接口
│   ├── ConfigurationService.cs     # 配置服务实现
│   ├── ILoggingService.cs          # 日志服务接口
│   ├── LoggingService.cs           # 日志服务实现
│   └── ServiceContainer.cs         # 服务容器
├── Helpers/               # 工具类
│   ├── LoggingHelper.cs   # 日志配置帮助类
│   └── CommandHelper.cs   # 命令帮助类
├── Converters/            # 值转换器
│   └── BooleanConverters.cs # 布尔值转换器
├── Resources/             # 资源文件
├── Styles/               # 样式定义
├── Controls/             # 自定义控件
├── Behaviors/            # 行为定义
└── appsettings.json      # 配置文件
```

## 🛠️ 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| .NET | 9.0 | 运行时框架 |
| WPF | - | UI框架 |
| CommunityToolkit.Mvvm | 8.4.0 | MVVM框架 |
| Serilog | 4.3.0 | 日志框架 |
| Microsoft.Extensions.DependencyInjection | 9.0.6 | 依赖注入 |
| Microsoft.Extensions.Configuration | 9.0.6 | 配置管理 |
| Microsoft.Extensions.Hosting | 9.0.6 | 主机服务 |

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd AirMonitor
```

### 2. 构建项目
```bash
dotnet build
```

### 3. 运行项目
```bash
dotnet run --project AirMonitor
```

## 📖 使用指南

### 配置管理
应用程序配置存储在 `appsettings.json` 文件中：

```json
{
  "logging": {
    "logLevel": "Information",
    "logFilePath": "Logs/app-.log",
    "retainedFileCountLimit": 30,
    "fileSizeLimitMB": 10
  },
  "application": {
    "name": "AirMonitor",
    "version": "1.0.0",
    "theme": "Light",
    "language": "zh-CN",
    "autoUpdate": true
  }
}
```

### 依赖注入
在 `ServiceContainer.cs` 中注册服务：

```csharp
// 注册服务
services.AddSingleton<IConfigurationService, ConfigurationService>();
services.AddSingleton<ILoggingService, LoggingService>();

// 注册ViewModels
services.AddTransient<MainViewModel>();
```

### ViewModelLocator使用
在XAML中启用自动绑定：

```xml
<Window xmlns:vm="clr-namespace:AirMonitor.ViewModels"
        vm:ViewModelLocator.AutoWireViewModel="True">
    <!-- 窗口内容 -->
</Window>
```

### 创建新的ViewModel
继承 `ViewModelBase` 类：

```csharp
public partial class MyViewModel : ViewModelBase
{
    public MyViewModel(ILoggingService loggingService) : base(loggingService)
    {
        Title = "我的ViewModel";
    }

    protected override async Task OnInitializeAsync()
    {
        // 初始化逻辑
    }
}
```

## 📝 日志系统

日志文件位置：`Logs/app-{date}.log`

日志级别：
- Verbose - 详细信息
- Debug - 调试信息
- Information - 一般信息
- Warning - 警告
- Error - 错误
- Fatal - 致命错误

## 🔧 开发规范

### 命名约定
- **Views**: `MainWindow`, `UserProfileView`, `SettingsPage`
- **ViewModels**: `MainViewModel`, `UserProfileViewModel`, `SettingsPageViewModel`
- **Services**: `IConfigurationService`, `ConfigurationService`
- **Models**: `AppSettings`, `UserProfile`

### 文件组织
- 每个文件只包含一个公共类
- 相关功能使用 `#region` 分组
- 避免过长的方法（不超过50行）

### 异常处理
- 仅捕获预期的异常
- 避免空catch块
- 记录异常信息用于调试

## 🧪 测试

建议为以下组件编写单元测试：
- ViewModels
- Services
- Converters
- Helpers

## 📄 许可证

本项目遵循 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请创建 Issue 或联系开发团队。
