using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 串口服务接口
/// </summary>
public interface ISerialPortService
{
    /// <summary>
    /// 串口连接状态变化事件
    /// </summary>
    event EventHandler<SerialPortConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// 串口列表变化事件（热插拔检测）
    /// </summary>
    event EventHandler<SerialPortListChangedEventArgs>? PortListChanged;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 当前连接配置
    /// </summary>
    SerialPortConnectionConfig CurrentConnectionConfig { get; }

    /// <summary>
    /// 当前连接状态
    /// </summary>
    SerialPortConnectionStatus ConnectionStatus { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 获取可用串口列表
    /// </summary>
    /// <returns>串口信息列表</returns>
    Task<List<SerialPortInfo>> GetAvailablePortsAsync();

    /// <summary>
    /// 检查串口是否可用（未被占用）
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <returns>是否可用</returns>
    Task<bool> IsPortAvailableAsync(string portName);

    /// <summary>
    /// 连接串口
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="protocolType">协议类型</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(string portName, SerialPortProtocolType protocolType);

    /// <summary>
    /// 断开串口连接
    /// </summary>
    /// <returns>断开是否成功</returns>
    Task<bool> DisconnectAsync();

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <returns>发送是否成功</returns>
    Task<bool> SendDataAsync(byte[] data);

    /// <summary>
    /// 发送字符串数据
    /// </summary>
    /// <param name="data">要发送的字符串</param>
    /// <returns>发送是否成功</returns>
    Task<bool> SendStringAsync(string data);

    /// <summary>
    /// 开始监控串口列表变化（热插拔检测）
    /// </summary>
    void StartPortMonitoring();

    /// <summary>
    /// 停止监控串口列表变化
    /// </summary>
    void StopPortMonitoring();

    /// <summary>
    /// 保存连接配置
    /// </summary>
    /// <param name="config">连接配置</param>
    /// <returns>保存是否成功</returns>
    Task<bool> SaveConnectionConfigAsync(SerialPortConnectionConfig config);

    /// <summary>
    /// 加载连接配置
    /// </summary>
    /// <returns>连接配置</returns>
    Task<SerialPortConnectionConfig> LoadConnectionConfigAsync();
}

/// <summary>
/// 串口连接状态变化事件参数
/// </summary>
public class SerialPortConnectionStatusChangedEventArgs : EventArgs
{
    public SerialPortConnectionStatus OldStatus { get; set; }
    public SerialPortConnectionStatus NewStatus { get; set; }
    public string PortName { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 串口列表变化事件参数
/// </summary>
public class SerialPortListChangedEventArgs : EventArgs
{
    public List<SerialPortInfo> AvailablePorts { get; set; } = new();
    public List<string> AddedPorts { get; set; } = new();
    public List<string> RemovedPorts { get; set; } = new();
}

/// <summary>
/// 串口数据接收事件参数
/// </summary>
public class SerialPortDataReceivedEventArgs : EventArgs
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string DataAsString { get; set; } = string.Empty;
    public DateTime ReceivedTime { get; set; } = DateTime.Now;
    public string PortName { get; set; } = string.Empty;
}
