using System.Windows;

namespace AirMonitor.Services;

/// <summary>
/// 对话框服务接口，提供模态和非模态窗口显示功能
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <param name="dialog">要显示的对话框窗口</param>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    bool? ShowDialog(Window dialog, Window? owner = null);

    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <typeparam name="T">对话框窗口类型</typeparam>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    bool? ShowDialog<T>(Window? owner = null) where T : Window, new();

    /// <summary>
    /// 显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <typeparam name="T">对话框窗口类型</typeparam>
    /// <param name="dialogFactory">对话框创建工厂方法</param>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    bool? ShowDialog<T>(Func<T> dialogFactory, Window? owner = null) where T : Window;

    /// <summary>
    /// 显示非模态独立窗口，不添加蒙版效果，不设置Owner
    /// </summary>
    /// <param name="window">要显示的窗口</param>
    void ShowWindow(Window window);

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果，可选择是否设置Owner
    /// </summary>
    /// <param name="window">要显示的窗口</param>
    /// <param name="owner">父窗口</param>
    /// <param name="setOwner">是否设置Owner属性</param>
    void ShowWindow(Window window, Window? owner, bool setOwner);

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果
    /// </summary>
    /// <typeparam name="T">窗口类型</typeparam>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>创建的窗口实例</returns>
    T ShowWindow<T>(Window? owner = null) where T : Window, new();

    /// <summary>
    /// 显示非模态窗口，不添加蒙版效果
    /// </summary>
    /// <typeparam name="T">窗口类型</typeparam>
    /// <param name="windowFactory">窗口创建工厂方法</param>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>创建的窗口实例</returns>
    T ShowWindow<T>(Func<T> windowFactory, Window? owner = null) where T : Window;

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <param name="dialog">要显示的对话框窗口</param>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    Task<bool?> ShowDialogAsync(Window dialog, Window? owner = null);

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <typeparam name="T">对话框窗口类型</typeparam>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    Task<bool?> ShowDialogAsync<T>(Window? owner = null) where T : Window, new();

    /// <summary>
    /// 异步显示模态对话框，带有半透明蒙版效果
    /// </summary>
    /// <typeparam name="T">对话框窗口类型</typeparam>
    /// <param name="dialogFactory">对话框创建工厂方法</param>
    /// <param name="owner">父窗口，如果为null则使用当前活动窗口</param>
    /// <returns>对话框的结果</returns>
    Task<bool?> ShowDialogAsync<T>(Func<T> dialogFactory, Window? owner = null) where T : Window;

    /// <summary>
    /// 获取当前活动的窗口
    /// </summary>
    /// <returns>当前活动的窗口，如果没有则返回null</returns>
    Window? GetActiveWindow();

    /// <summary>
    /// 检查指定窗口是否有活动的模态对话框
    /// </summary>
    /// <param name="owner">要检查的窗口</param>
    /// <returns>如果有活动的模态对话框则返回true</returns>
    bool HasActiveDialog(Window owner);
}
