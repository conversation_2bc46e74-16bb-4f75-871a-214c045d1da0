namespace AirMonitor.Services;

/// <summary>
/// 数据桥接服务接口
/// 负责连接串口服务和数据分包器服务，将串口接收的数据传递给分包器进行处理
/// </summary>
public interface IDataBridgeService
{
    /// <summary>
    /// 是否已启动数据桥接
    /// </summary>
    bool IsStarted { get; }

    /// <summary>
    /// 启动数据桥接服务
    /// </summary>
    void Start();

    /// <summary>
    /// 停止数据桥接服务
    /// </summary>
    void Stop();

    /// <summary>
    /// 获取桥接统计信息
    /// </summary>
    /// <returns>统计信息字典</returns>
    Dictionary<string, object> GetStatistics();
}
