﻿<Window
    x:Class="AirMonitor.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding Title, FallbackValue='AirMonitor'}"
    Width="1280"
    Height="768"
    MinWidth="1280"
    MinHeight="768"
    Style="{StaticResource MainWindowStyle}"
    vm:ViewModelLocator.AutoWireViewModel="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <DockPanel>
        <!--  菜单栏  -->
        <Menu
            DockPanel.Dock="Top"
            Style="{StaticResource MainMenuStyle}">
            <MenuItem
                Header="数据源(_D)"
                Style="{StaticResource MainMenuItemStyle}">
                <MenuItem
                    Header="串口连接(_S)"
                    Command="{Binding SerialPortConnectionCommand}" />
            </MenuItem>
            <MenuItem
                Header="监听(_L)"
                Style="{StaticResource MainMenuItemStyle}">
                <MenuItem
                    Header="帧数据监听(_F)"
                    Command="{Binding FrameDataListenerCommand}" />
            </MenuItem>
            <MenuItem
                Header="工具(_T)"
                Style="{StaticResource MainMenuItemStyle}">
                <MenuItem
                    Header="协议数据解析配置(_P)"
                    Command="{Binding ProtocolParseConfigCommand}" />
            </MenuItem>
            <MenuItem
                Header="帮助(_H)"
                Style="{StaticResource MainMenuItemStyle}">
                <MenuItem
                    Header="关于(_A)"
                    Command="{Binding AboutCommand}" />
            </MenuItem>
        </Menu>

        <!--  工具栏  -->
        <ToolBarTray
            DockPanel.Dock="Top"
            Style="{StaticResource MainToolBarTrayStyle}">
            <ToolBar Style="{StaticResource MainToolBarStyle}">
                <!--  工具栏按钮将在后续添加  -->
                <TextBlock
                    VerticalAlignment="Center"
                    Foreground="#888888"
                    Text="工具栏 - 常用功能按钮将在此处添加" />
            </ToolBar>
        </ToolBarTray>

        <!--  状态栏  -->
        <StatusBar
            DockPanel.Dock="Bottom"
            Style="{StaticResource MainStatusBarStyle}">
            <StatusBarItem Style="{StaticResource StatusBarItemStyle}">
                <TextBlock
                    Style="{StaticResource StatusBarTextStyle}"
                    Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem
                HorizontalAlignment="Right"
                Style="{StaticResource StatusBarItemStyle}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Style="{StaticResource StatusBarTextStyle}"
                        Text="版本: " />
                    <TextBlock
                        Style="{StaticResource StatusBarTextStyle}"
                        Text="{Binding ApplicationVersion}" />
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

        <!--  主内容区域  -->
        <Grid Style="{StaticResource ResponsiveGridStyle}">
            <Border Style="{StaticResource MainContentBorderStyle}">
                <Grid>
                    <Viewbox
                        MaxWidth="400"
                        MaxHeight="200"
                        Stretch="Uniform">
                        <TextBlock
                            Style="{StaticResource PlaceholderTextStyle}"
                            Text="主内容区域&#x0a;&#x0a;业务功能将在此处显示&#x0a;&#x0a;支持响应式布局，最小分辨率 1280×768" />
                    </Viewbox>
                </Grid>
            </Border>
        </Grid>
    </DockPanel>
</Window>
