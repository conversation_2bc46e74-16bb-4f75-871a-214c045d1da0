# 协议数据解析配置系统使用说明

## 概述

协议数据解析配置系统是AirMonitor项目中用于定义如何将接收到的帧数据解析成具体物理量数据的核心功能。该系统支持复杂的数据映射场景，包括字节级映射、位级映射、跨字节位拼接和基于索引(idx)的复杂映射。

## 功能特性

### 1. 数据映射类型支持
- **字节级映射**：将帧数据中的单个或多个连续字节映射到一个物理量
- **位级映射**：将帧数据中某个字节的特定位或多个位映射到一个物理量
- **跨字节位拼接**：将不相邻字节中的特定位组合拼接成一个物理量的值
- **索引映射**：基于帧中的idx值进行动态映射

### 2. 物理量管理
- 支持多种数据类型（UInt8, Int8, UInt16, Int16, UInt32, Int32, Float, Double, Bo<PERSON>an, String）
- 支持缩放因子和偏移量计算
- 支持有效值范围验证
- 支持分组管理和排序

### 3. 配置管理
- 配置的导入导出功能
- 批量配置管理
- 配置验证和测试
- 示例配置加载

## 使用指南

### 1. 打开配置界面

在主窗口菜单栏中选择：**工具(T)** → **协议数据解析配置(P)**

### 2. 加载示例配置

首次使用时，建议先加载示例配置来了解系统功能：

1. 点击工具栏中的 **"加载示例"** 按钮
2. 确认加载示例配置
3. 系统将自动加载预定义的示例配置，包括：
   - 商用内外机通讯协议示例配置
   - 模块机通讯协议示例配置
   - 默认物理量定义

### 3. 创建新配置

#### 3.1 基本信息配置
1. 点击 **"新建配置"** 按钮
2. 填写配置基本信息：
   - **配置名称**：为配置起一个有意义的名称
   - **协议类型**：选择商用协议或模块协议
   - **命令码**：指定该配置适用的命令码（可选）
   - **版本**：配置版本号
   - **描述**：详细描述配置的用途

#### 3.2 定义物理量
1. 切换到 **"物理量定义"** 选项卡
2. 点击 **"新建物理量"** 按钮
3. 填写物理量信息：
   - **物理量名称**：唯一标识符（英文）
   - **显示名称**：用户友好的中文名称
   - **数据类型**：选择合适的数据类型
   - **单位**：物理量的单位
   - **缩放因子**：原始值乘以此因子得到实际值
   - **偏移量**：在缩放后加上此偏移量
   - **有效范围**：设置最小值和最大值
   - **小数位数**：显示时的小数位数
   - **分组名称**：用于界面分组显示

#### 3.3 配置数据映射
1. 切换到 **"数据映射配置"** 选项卡
2. 点击 **"新建数据映射"** 按钮
3. 根据映射类型配置：

**字节级映射示例**：
- 映射类型：字节级映射
- 目标物理量：选择已定义的物理量
- 字节范围：起始索引=0，长度=2，字节序=小端序

**位级映射示例**：
- 映射类型：位级映射
- 目标物理量：选择已定义的物理量
- 位位置：字节索引=4，位索引=0，位长度=1

**跨字节位拼接示例**：
- 映射类型：跨字节位拼接
- 目标物理量：选择已定义的物理量
- 位位置列表：配置多个位位置，指定目标位偏移

### 4. 配置验证和测试

#### 4.1 验证配置
点击 **"验证配置"** 按钮，系统将检查：
- 配置基本信息的完整性
- 物理量定义的有效性
- 数据映射的正确性
- 交叉引用的一致性

#### 4.2 测试配置
点击 **"测试配置"** 按钮，系统将：
- 生成测试数据
- 模拟数据解析过程
- 显示解析结果
- 报告任何问题

### 5. 配置导入导出

#### 5.1 导出配置
1. 选择要导出的配置
2. 点击 **"导出配置"** 按钮
3. 选择保存位置和文件名
4. 配置将以JSON格式保存

#### 5.2 导入配置
1. 点击 **"导入配置"** 按钮
2. 选择要导入的JSON配置文件
3. 系统将自动处理名称冲突
4. 导入成功后配置将出现在列表中

#### 5.3 批量操作
- **批量导入**：一次导入多个配置文件
- **导出全部**：将所有配置导出到一个文件

## 示例配置说明

### 商用内外机通讯协议示例

该示例展示了典型的空调内外机通讯数据解析：

- **室内温度**：字节0-1，16位有符号整数，缩放因子0.1
- **室外温度**：字节2-3，16位有符号整数，缩放因子0.1
- **压缩机状态**：字节4的第0位，布尔值
- **风机转速**：字节4的第1-3位，3位数值
- **系统模式**：字节4的第4-6位，3位数值
- **故障代码**：字节5-6，16位无符号整数

### 模块机通讯协议示例

该示例展示了基于索引的动态数据映射：

- **索引值解析**：字节0-1为索引值
- **索引数量**：字节2-3为索引数量
- **动态数据**：根据索引值确定字节4-5的含义
  - 索引1：温度数据
  - 索引2：压力数据
  - 索引3：电压数据

## 注意事项

1. **配置名称唯一性**：确保配置名称在系统中唯一
2. **物理量名称规范**：使用英文标识符，避免特殊字符
3. **数据类型匹配**：确保映射的数据类型与物理量定义匹配
4. **字节索引范围**：注意字节索引不要超出实际数据帧长度
5. **位操作正确性**：位索引范围为0-7，位长度不超过8
6. **缩放因子设置**：避免设置为0，可能导致计算错误

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查物理量名称是否重复
   - 确认数据映射引用的物理量存在
   - 验证字节范围和位位置的有效性

2. **测试配置失败**
   - 检查数据类型转换是否正确
   - 确认字节索引没有越界
   - 验证位操作的正确性

3. **导入配置失败**
   - 确认JSON文件格式正确
   - 检查文件编码是否为UTF-8
   - 验证配置内容的完整性

### 调试建议

1. 使用示例配置作为参考
2. 先创建简单的映射，再逐步增加复杂度
3. 利用测试功能验证配置正确性
4. 查看日志文件获取详细错误信息

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看应用程序日志文件
2. 使用配置验证和测试功能
3. 参考示例配置
4. 联系技术支持团队
