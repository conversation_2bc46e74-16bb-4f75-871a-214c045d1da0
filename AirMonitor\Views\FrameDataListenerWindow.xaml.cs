using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using AirMonitor.ViewModels;
using AirMonitor.Models;

namespace AirMonitor.Views;

/// <summary>
/// 帧数据监听窗体
/// </summary>
public partial class FrameDataListenerWindow : Window
{
    private FrameDataListenerViewModel? _viewModel;

    public FrameDataListenerWindow()
    {
        InitializeComponent();
        Loaded += OnLoaded;
        Closing += OnClosing;
    }

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 如果DataContext是FrameDataListenerViewModel，则初始化它
        if (DataContext is FrameDataListenerViewModel viewModel)
        {
            _viewModel = viewModel;
            await viewModel.InitializeAsync();

            // 订阅集合变化事件以实现自动滚动
            viewModel.FrameDataItems.CollectionChanged += OnFrameDataItemsChanged;
        }
    }

    /// <summary>
    /// 窗口关闭事件处理
    /// </summary>
    private void OnClosing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        // 如果DataContext是FrameDataListenerViewModel，则释放资源
        if (_viewModel != null)
        {
            _viewModel.FrameDataItems.CollectionChanged -= OnFrameDataItemsChanged;
            _viewModel.Dispose();
        }
    }

    /// <summary>
    /// 帧数据项集合变化事件处理
    /// </summary>
    private void OnFrameDataItemsChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        if (_viewModel?.AutoScrollToBottom == true &&
            e.Action == NotifyCollectionChangedAction.Add &&
            e.NewItems?.Count > 0)
        {
            // 自动滚动到最新项
            Dispatcher.BeginInvoke(() =>
            {
                if (FrameDataGrid.Items.Count > 0)
                {
                    var lastItem = FrameDataGrid.Items[FrameDataGrid.Items.Count - 1];
                    FrameDataGrid.ScrollIntoView(lastItem);
                }
            }, System.Windows.Threading.DispatcherPriority.Background);
        }
    }

    /// <summary>
    /// DataGrid选择变化事件处理
    /// </summary>
    private void FrameDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (_viewModel != null)
        {
            // 清空ViewModel中的选中项集合
            _viewModel.SelectedItems.Clear();

            // 添加当前选中的项
            foreach (FrameDataItem item in FrameDataGrid.SelectedItems)
            {
                _viewModel.SelectedItems.Add(item);
            }
        }
    }
}
