<UserControl
    x:Class="AirMonitor.Controls.BytePositionSelector"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="200" d:DesignWidth="400">

    <UserControl.Resources>
        <Style x:Key="ByteIndexTextBoxStyle" TargetType="TextBox">
            <Setter Property="Width" Value="60" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="TextAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>

        <Style x:Key="BitButtonStyle" TargetType="ToggleButton">
            <Setter Property="Width" Value="25" />
            <Setter Property="Height" Value="25" />
            <Setter Property="Margin" Value="1" />
            <Setter Property="FontSize" Value="10" />
            <Setter Property="FontWeight" Value="Bold" />
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="Background" Value="#4CAF50" />
                    <Setter Property="Foreground" Value="White" />
                </Trigger>
                <Trigger Property="IsChecked" Value="False">
                    <Setter Property="Background" Value="#F5F5F5" />
                    <Setter Property="Foreground" Value="Black" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,5,0,5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </UserControl.Resources>

    <Border BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4" Padding="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" Text="字节位置选择器" Style="{StaticResource SectionHeaderStyle}" FontSize="14" />

            <!-- 字节索引输入 -->
            <Grid Grid.Row="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="字节索引:" VerticalAlignment="Center" Margin="0,0,10,0" />
                <TextBox Grid.Column="1" 
                         Text="{Binding ByteIndex, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ByteIndexTextBoxStyle}" />

                <TextBlock Grid.Column="2" Text="位长度:" VerticalAlignment="Center" Margin="20,0,10,0" />
                <TextBox Grid.Column="3" 
                         Text="{Binding BitLength, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ByteIndexTextBoxStyle}" />
            </Grid>

            <!-- 位选择标题 -->
            <TextBlock Grid.Row="2" Text="位选择 (0=最低位, 7=最高位):" Style="{StaticResource SectionHeaderStyle}" />

            <!-- 位选择按钮 -->
            <Grid Grid.Row="3" Margin="0,5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- 位索引标签 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,5">
                    <TextBlock Text="7" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" />
                    <TextBlock Text="6" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="5" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="4" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="3" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="2" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="1" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                    <TextBlock Text="0" Width="25" TextAlignment="Center" FontSize="10" Foreground="Gray" Margin="2,0" />
                </StackPanel>

                <!-- 位选择按钮 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <ToggleButton x:Name="Bit7" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="7" />
                    <ToggleButton x:Name="Bit6" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="6" />
                    <ToggleButton x:Name="Bit5" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="5" />
                    <ToggleButton x:Name="Bit4" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="4" />
                    <ToggleButton x:Name="Bit3" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="3" />
                    <ToggleButton x:Name="Bit2" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="2" />
                    <ToggleButton x:Name="Bit1" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="1" />
                    <ToggleButton x:Name="Bit0" Content="0" Style="{StaticResource BitButtonStyle}" Click="OnBitButtonClick" Tag="0" />
                </StackPanel>
            </Grid>

            <!-- 结果显示 -->
            <Grid Grid.Row="4" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="起始位:" VerticalAlignment="Center" Margin="0,0,10,0" />
                <TextBox Grid.Column="1" 
                         Text="{Binding BitIndex, Mode=OneWay}" 
                         IsReadOnly="True"
                         Style="{StaticResource ByteIndexTextBoxStyle}" 
                         Background="#F5F5F5" />

                <TextBlock Grid.Column="2" Text="位偏移:" VerticalAlignment="Center" Margin="20,0,10,0" />
                <TextBox Grid.Column="3" 
                         Text="{Binding TargetBitOffset, UpdateSourceTrigger=PropertyChanged}" 
                         Style="{StaticResource ByteIndexTextBoxStyle}" />
            </Grid>
        </Grid>
    </Border>
</UserControl>
