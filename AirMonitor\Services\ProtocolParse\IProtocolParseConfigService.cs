using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 协议数据解析配置服务接口
/// </summary>
public interface IProtocolParseConfigService
{
    #region 配置管理

    /// <summary>
    /// 获取所有协议解析配置
    /// </summary>
    /// <returns>配置列表</returns>
    Task<List<ProtocolParseConfig>> GetAllConfigsAsync();

    /// <summary>
    /// 根据ID获取协议解析配置
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns>配置对象，如果不存在则返回null</returns>
    Task<ProtocolParseConfig?> GetConfigByIdAsync(string configId);

    /// <summary>
    /// 根据协议类型获取配置列表
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <returns>配置列表</returns>
    Task<List<ProtocolParseConfig>> GetConfigsByProtocolTypeAsync(DataPacketProtocolType protocolType);

    /// <summary>
    /// 根据协议类型和命令码获取配置
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <param name="commandCode">命令码</param>
    /// <returns>配置对象，如果不存在则返回null</returns>
    Task<ProtocolParseConfig?> GetConfigByProtocolAndCommandAsync(DataPacketProtocolType protocolType, byte commandCode);

    /// <summary>
    /// 保存协议解析配置
    /// </summary>
    /// <param name="config">配置对象</param>
    /// <returns>保存是否成功</returns>
    Task<bool> SaveConfigAsync(ProtocolParseConfig config);

    /// <summary>
    /// 删除协议解析配置
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeleteConfigAsync(string configId);

    /// <summary>
    /// 复制协议解析配置
    /// </summary>
    /// <param name="sourceConfigId">源配置ID</param>
    /// <param name="newConfigName">新配置名称</param>
    /// <returns>新配置对象，如果复制失败则返回null</returns>
    Task<ProtocolParseConfig?> CopyConfigAsync(string sourceConfigId, string newConfigName);

    #endregion

    #region 配置验证

    /// <summary>
    /// 验证协议解析配置
    /// </summary>
    /// <param name="config">配置对象</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateConfigAsync(ProtocolParseConfig config);

    /// <summary>
    /// 检查配置名称是否已存在
    /// </summary>
    /// <param name="configName">配置名称</param>
    /// <param name="excludeConfigId">排除的配置ID（用于编辑时检查）</param>
    /// <returns>是否已存在</returns>
    Task<bool> IsConfigNameExistsAsync(string configName, string? excludeConfigId = null);

    #endregion

    #region 导入导出

    /// <summary>
    /// 导出配置到JSON文件
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <param name="filePath">文件路径</param>
    /// <returns>导出是否成功</returns>
    Task<bool> ExportConfigToFileAsync(string configId, string filePath);

    /// <summary>
    /// 从JSON文件导入配置
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>导入的配置对象，如果导入失败则返回null</returns>
    Task<ProtocolParseConfig?> ImportConfigFromFileAsync(string filePath);

    /// <summary>
    /// 导出所有配置到JSON文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>导出是否成功</returns>
    Task<bool> ExportAllConfigsToFileAsync(string filePath);

    /// <summary>
    /// 从JSON文件批量导入配置
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>导入结果（成功数量，失败数量，错误信息）</returns>
    Task<(int successCount, int failCount, List<string> errors)> ImportConfigsFromFileAsync(string filePath);

    #endregion

    #region 物理量管理

    /// <summary>
    /// 获取所有物理量定义
    /// </summary>
    /// <returns>物理量列表</returns>
    Task<List<PhysicalQuantity>> GetAllPhysicalQuantitiesAsync();

    /// <summary>
    /// 根据分组获取物理量列表
    /// </summary>
    /// <param name="groupName">分组名称</param>
    /// <returns>物理量列表</returns>
    Task<List<PhysicalQuantity>> GetPhysicalQuantitiesByGroupAsync(string groupName);

    /// <summary>
    /// 保存物理量定义
    /// </summary>
    /// <param name="physicalQuantity">物理量对象</param>
    /// <returns>保存是否成功</returns>
    Task<bool> SavePhysicalQuantityAsync(PhysicalQuantity physicalQuantity);

    /// <summary>
    /// 删除物理量定义
    /// </summary>
    /// <param name="physicalQuantityName">物理量名称</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeletePhysicalQuantityAsync(string physicalQuantityName);

    /// <summary>
    /// 获取物理量分组列表
    /// </summary>
    /// <returns>分组列表</returns>
    Task<List<PhysicalQuantityGroup>> GetPhysicalQuantityGroupsAsync();

    #endregion

    #region 配置应用

    /// <summary>
    /// 获取当前活动的配置
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <returns>活动配置列表</returns>
    Task<List<ProtocolParseConfig>> GetActiveConfigsAsync(DataPacketProtocolType protocolType);

    /// <summary>
    /// 设置配置的激活状态
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <param name="isActive">是否激活</param>
    /// <returns>设置是否成功</returns>
    Task<bool> SetConfigActiveStatusAsync(string configId, bool isActive);

    #endregion

    #region 事件

    /// <summary>
    /// 配置变更事件
    /// </summary>
    event EventHandler<ProtocolParseConfigChangedEventArgs>? ConfigChanged;

    /// <summary>
    /// 物理量变更事件
    /// </summary>
    event EventHandler<PhysicalQuantityChangedEventArgs>? PhysicalQuantityChanged;

    #endregion
}

/// <summary>
/// 协议解析配置变更事件参数
/// </summary>
public class ProtocolParseConfigChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变更类型
    /// </summary>
    public ConfigChangeType ChangeType { get; set; }

    /// <summary>
    /// 配置对象
    /// </summary>
    public ProtocolParseConfig Config { get; set; } = new();

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangeTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 物理量变更事件参数
/// </summary>
public class PhysicalQuantityChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变更类型
    /// </summary>
    public ConfigChangeType ChangeType { get; set; }

    /// <summary>
    /// 物理量对象
    /// </summary>
    public PhysicalQuantity PhysicalQuantity { get; set; } = new();

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangeTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 配置变更类型枚举
/// </summary>
public enum ConfigChangeType
{
    /// <summary>
    /// 添加
    /// </summary>
    Added,

    /// <summary>
    /// 更新
    /// </summary>
    Updated,

    /// <summary>
    /// 删除
    /// </summary>
    Deleted,

    /// <summary>
    /// 激活状态变更
    /// </summary>
    ActiveStatusChanged
}
