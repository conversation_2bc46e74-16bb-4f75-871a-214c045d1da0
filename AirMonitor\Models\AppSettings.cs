using System.ComponentModel.DataAnnotations;

namespace AirMonitor.Models;

/// <summary>
/// 应用程序配置模型
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingSettings Logging { get; set; } = new();

    /// <summary>
    /// 应用程序配置
    /// </summary>
    public ApplicationSettings Application { get; set; } = new();
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingSettings
{
    /// <summary>
    /// 日志级别 (Verbose, Debug, Information, Warning, Error, Fatal)
    /// </summary>
    [Required]
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 日志文件路径
    /// </summary>
    [Required]
    public string LogFilePath { get; set; } = "Logs/app-.log";

    /// <summary>
    /// 日志文件保留天数
    /// </summary>
    [Range(1, 365)]
    public int RetainedFileCountLimit { get; set; } = 30;

    /// <summary>
    /// 单个日志文件最大大小（MB）
    /// </summary>
    [Range(1, 1024)]
    public int FileSizeLimitMB { get; set; } = 10;
}

/// <summary>
/// 应用程序配置
/// </summary>
public class ApplicationSettings
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    [Required]
    public string Name { get; set; } = "AirMonitor";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    [Required]
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 主题设置
    /// </summary>
    public string Theme { get; set; } = "Light";

    /// <summary>
    /// 语言设置
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 是否启用自动更新
    /// </summary>
    public bool AutoUpdate { get; set; } = true;
}
