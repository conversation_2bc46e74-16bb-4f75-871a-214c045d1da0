﻿using System.Windows;
using AirMonitor.Services;
using AirMonitor.Helpers;

namespace AirMonitor;

/// <summary>
/// 应用程序主类
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 初始化服务容器
            ServiceContainer.Initialize();

            // 获取配置服务并加载配置
            var configService = ServiceContainer.GetService<IConfigurationService>();
            configService.LoadConfigurationAsync().Wait();

            // 配置日志系统
            LoggingHelper.ConfigureSerilog(configService.AppSettings.Logging);
            LoggingHelper.LogApplicationStart(
                configService.AppSettings.Application.Name,
                configService.AppSettings.Application.Version);

            // 设置全局异常处理
            SetupGlobalExceptionHandling();

            // 启动数据桥接服务
            StartDataBridgeService();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            LoggingHelper.LogUnhandledException(ex, "应用程序启动");
            MessageBox.Show($"应用程序启动失败:\n{ex.Message}", "启动错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        try
        {
            // 停止数据桥接服务
            StopDataBridgeService();

            LoggingHelper.LogApplicationShutdown("AirMonitor");
            LoggingHelper.CloseAndFlush();
            ServiceContainer.Dispose();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"应用程序退出时发生错误: {ex.Message}");
        }
        finally
        {
            base.OnExit(e);
        }
    }

    /// <summary>
    /// 启动数据桥接服务
    /// </summary>
    private void StartDataBridgeService()
    {
        try
        {
            var dataBridgeService = ServiceContainer.GetService<IDataBridgeService>();
            dataBridgeService.Start();
        }
        catch (Exception ex)
        {
            LoggingHelper.LogUnhandledException(ex, "启动数据桥接服务");
        }
    }

    /// <summary>
    /// 停止数据桥接服务
    /// </summary>
    private void StopDataBridgeService()
    {
        try
        {
            var dataBridgeService = ServiceContainer.GetServiceOrNull<IDataBridgeService>();
            dataBridgeService?.Stop();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"停止数据桥接服务时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 设置全局异常处理
    /// </summary>
    private void SetupGlobalExceptionHandling()
    {
        // 处理UI线程未捕获的异常
        DispatcherUnhandledException += (sender, e) =>
        {
            LoggingHelper.LogUnhandledException(e.Exception, "UI线程未捕获异常");

            MessageBox.Show($"发生未处理的错误:\n{e.Exception.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);

            e.Handled = true; // 防止应用程序崩溃
        };

        // 处理非UI线程未捕获的异常
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            if (e.ExceptionObject is Exception exception)
            {
                LoggingHelper.LogUnhandledException(exception, "非UI线程未捕获异常");
            }
        };

        // 处理Task中未观察到的异常
        TaskScheduler.UnobservedTaskException += (sender, e) =>
        {
            LoggingHelper.LogUnhandledException(e.Exception, "Task未观察到的异常");
            e.SetObserved(); // 防止应用程序崩溃
        };
    }
}

