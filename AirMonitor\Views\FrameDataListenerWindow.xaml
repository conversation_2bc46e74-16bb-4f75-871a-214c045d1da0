﻿<Window
    x:Class="AirMonitor.Views.FrameDataListenerWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="帧数据监听"
    Width="1000"
    Height="600"
    MinWidth="800"
    MinHeight="400"
    Style="{StaticResource FrameDataListenerWindowStyle}"
    vm:ViewModelLocator.AutoWireViewModel="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 控制面板 -->
        <Border
            Grid.Row="0"
            Style="{StaticResource ControlPanelStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- 左侧控制按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button
                        Command="{Binding PauseResumeCommand}"
                        Content="{Binding PauseResumeButtonText}"
                        Style="{StaticResource ControlButtonStyle}" />
                    <Button
                        Command="{Binding CopySelectedCommand}"
                        Content="复制选中"
                        Style="{StaticResource ControlButtonStyle}" />
                    <Button
                        Command="{Binding ExportCommand}"
                        Content="导出数据"
                        Style="{StaticResource ControlButtonStyle}" />
                    <Button
                        Command="{Binding ClearCommand}"
                        Content="清空数据"
                        Style="{StaticResource ControlButtonStyle}" />
                </StackPanel>

                <!-- 右侧设置 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock
                        Style="{StaticResource StatusTextStyle}"
                        Text="最大显示条数:" />
                    <TextBox
                        Width="80"
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Style="{StaticResource ControlTextBoxStyle}"
                        Text="{Binding MaxDisplayCount, UpdateSourceTrigger=PropertyChanged}" />
                    <CheckBox
                        Content="自动滚动到最新"
                        IsChecked="{Binding AutoScrollToBottom}"
                        Style="{StaticResource ControlCheckBoxStyle}" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- 数据显示区域 -->
        <DataGrid
            x:Name="FrameDataGrid"
            Grid.Row="1"
            ItemsSource="{Binding FrameDataItems}"
            Style="{StaticResource FrameDataGridStyle}"
            ColumnHeaderStyle="{StaticResource FrameDataGridColumnHeaderStyle}"
            RowStyle="{StaticResource FrameDataGridRowStyle}"
            CellStyle="{StaticResource FrameDataGridCellStyle}"
            SelectionChanged="FrameDataGrid_SelectionChanged">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="150"
                    Binding="{Binding TimestampString}"
                    Header="时间戳"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Width="80"
                    Binding="{Binding ProtocolType}"
                    Header="协议类型"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Width="80"
                    Binding="{Binding PortName}"
                    Header="端口"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Width="60"
                    Binding="{Binding CrcStatusText}"
                    Header="CRC"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding HexData}"
                    Header="16进制数据"
                    IsReadOnly="True" />
            </DataGrid.Columns>
        </DataGrid>

        <!-- 状态栏 -->
        <Border
            Grid.Row="2"
            Style="{StaticResource StatusBarStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    Style="{StaticResource StatusTextStyle}"
                    Text="{Binding StatusMessage}" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock
                        Style="{StaticResource StatusTextStyle}"
                        Text="总帧数:" />
                    <TextBlock
                        Style="{StaticResource StatusTextStyle}"
                        Text="{Binding TotalFrameCount}" />
                    <TextBlock
                        Margin="20,0,0,0"
                        Style="{StaticResource StatusTextStyle}"
                        Text="显示帧数:" />
                    <TextBlock
                        Style="{StaticResource StatusTextStyle}"
                        Text="{Binding DisplayedFrameCount}" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
