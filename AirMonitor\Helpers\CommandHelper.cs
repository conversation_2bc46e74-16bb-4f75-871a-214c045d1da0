using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;

namespace AirMonitor.Helpers;

/// <summary>
/// 命令帮助类，提供创建命令的便捷方法
/// </summary>
public static class CommandHelper
{
    /// <summary>
    /// 创建同步命令
    /// </summary>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>命令</returns>
    public static ICommand CreateCommand(Action execute, Func<bool>? canExecute = null)
    {
        return new RelayCommand(execute, canExecute!);
    }

    /// <summary>
    /// 创建带参数的同步命令
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>命令</returns>
    public static ICommand CreateCommand<T>(Action<T?> execute, Predicate<T?>? canExecute = null)
    {
        return new RelayCommand<T>(execute, canExecute!);
    }

    /// <summary>
    /// 创建异步命令
    /// </summary>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>异步命令</returns>
    public static IAsyncRelayCommand CreateAsyncCommand(Func<Task> execute, Func<bool>? canExecute = null)
    {
        return new AsyncRelayCommand(execute, canExecute!);
    }

    /// <summary>
    /// 创建带参数的异步命令
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>异步命令</returns>
    public static IAsyncRelayCommand CreateAsyncCommand<T>(Func<T?, Task> execute, Predicate<T?>? canExecute = null)
    {
        return new AsyncRelayCommand<T>(execute, canExecute!);
    }

    /// <summary>
    /// 创建带取消支持的异步命令
    /// </summary>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>异步命令</returns>
    public static IAsyncRelayCommand CreateCancellableAsyncCommand(
        Func<CancellationToken, Task> execute,
        Func<bool>? canExecute = null)
    {
        return new AsyncRelayCommand(execute, canExecute!);
    }

    /// <summary>
    /// 创建带参数和取消支持的异步命令
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="execute">执行方法</param>
    /// <param name="canExecute">是否可执行方法</param>
    /// <returns>异步命令</returns>
    public static IAsyncRelayCommand CreateCancellableAsyncCommand<T>(
        Func<T?, CancellationToken, Task> execute,
        Predicate<T?>? canExecute = null)
    {
        return new AsyncRelayCommand<T>(execute, canExecute!);
    }
}

/// <summary>
/// 命令扩展方法
/// </summary>
public static class CommandExtensions
{
    /// <summary>
    /// 安全执行命令
    /// </summary>
    /// <param name="command">命令</param>
    /// <param name="parameter">参数</param>
    public static void SafeExecute(this ICommand command, object? parameter = null)
    {
        if (command?.CanExecute(parameter) == true)
        {
            command.Execute(parameter);
        }
    }

    /// <summary>
    /// 安全执行异步命令
    /// </summary>
    /// <param name="command">异步命令</param>
    /// <param name="parameter">参数</param>
    /// <returns>任务</returns>
    public static Task SafeExecuteAsync(this IAsyncRelayCommand command, object? parameter = null)
    {
        if (command?.CanExecute(parameter) == true)
        {
            return command.ExecuteAsync(parameter);
        }
        return Task.CompletedTask;
    }
}
