<Window
    x:Class="AirMonitor.Views.DialogWindowBase"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding Title, RelativeSource={RelativeSource Self}}"
    Width="600"
    Height="400"
    MinWidth="300"
    MinHeight="200"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    Foreground="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"
    ResizeMode="CanResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    WindowStyle="SingleBorderWindow"
    mc:Ignorable="d">

    <Window.Resources>

        <!--  对话框样式  -->
        <Style x:Key="DialogButtonStyle" TargetType="Button">
            <Setter Property="MinWidth" Value="75" />
            <Setter Property="MinHeight" Value="23" />
            <Setter Property="Margin" Value="3" />
            <Setter Property="Padding" Value="8,2" />
        </Style>

        <!--  标题栏样式  -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,0,0,10" />
            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}" />
        </Style>

        <!--  内容区域样式  -->
        <Style x:Key="DialogContentStyle" TargetType="ContentPresenter">
            <Setter Property="Margin" Value="0,0,0,15" />
        </Style>

        <!--  按钮面板样式  -->
        <Style x:Key="DialogButtonPanelStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal" />
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="Margin" Value="0,10,0,0" />
        </Style>

        <!--  窗口显示动画  -->
        <Storyboard x:Key="ShowAnimation">
            <DoubleAnimation
                Storyboard.TargetProperty="Opacity"
                From="0"
                To="1"
                Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                From="0.9"
                To="1"
                Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation
                Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                From="0.9"
                To="1"
                Duration="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut" />
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </Window.Resources>

    <Window.RenderTransform>
        <ScaleTransform ScaleX="1" ScaleY="1" />
    </Window.RenderTransform>

    <Window.RenderTransformOrigin>
        <Point X="0.5" Y="0.5" />
    </Window.RenderTransformOrigin>

    <Border Padding="20">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题区域  -->
            <TextBlock
                x:Name="DialogTitleTextBlock"
                Grid.Row="0"
                Style="{StaticResource DialogTitleStyle}"
                Text="{Binding DialogTitle, RelativeSource={RelativeSource AncestorType=Window}}"
                Visibility="{Binding ShowDialogTitle, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}" />

            <!--  内容区域  -->
            <ContentPresenter
                x:Name="DialogContentPresenter"
                Grid.Row="1"
                Content="{Binding DialogContent, RelativeSource={RelativeSource AncestorType=Window}}"
                Style="{StaticResource DialogContentStyle}" />

            <!--  按钮区域  -->
            <StackPanel
                x:Name="ButtonPanel"
                Grid.Row="2"
                Style="{StaticResource DialogButtonPanelStyle}"
                Visibility="{Binding ShowButtons, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}">

                <Button
                    x:Name="OkButton"
                    Click="OkButton_Click"
                    Content="确定"
                    IsDefault="True"
                    Style="{StaticResource DialogButtonStyle}"
                    Visibility="{Binding ShowOkButton, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}" />

                <Button
                    x:Name="CancelButton"
                    Click="CancelButton_Click"
                    Content="取消"
                    IsCancel="True"
                    Style="{StaticResource DialogButtonStyle}"
                    Visibility="{Binding ShowCancelButton, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}" />
            </StackPanel>
        </Grid>
    </Border>
</Window>
