using System.Windows;
using System.Windows.Media.Animation;

namespace AirMonitor.Views;

/// <summary>
/// 对话框窗口基类，提供统一的样式和行为
/// </summary>
public partial class DialogWindowBase : Window
{
    #region 依赖属性

    /// <summary>
    /// 对话框标题依赖属性
    /// </summary>
    public static readonly DependencyProperty DialogTitleProperty =
        DependencyProperty.Register(
            nameof(DialogTitle),
            typeof(string),
            typeof(DialogWindowBase),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// 对话框内容依赖属性
    /// </summary>
    public static readonly DependencyProperty DialogContentProperty =
        DependencyProperty.Register(
            nameof(DialogContent),
            typeof(object),
            typeof(DialogWindowBase),
            new PropertyMetadata(null));

    /// <summary>
    /// 是否显示对话框标题依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowDialogTitleProperty =
        DependencyProperty.Register(
            nameof(ShowDialogTitle),
            typeof(bool),
            typeof(DialogWindowBase),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示按钮依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowButtonsProperty =
        DependencyProperty.Register(
            nameof(ShowButtons),
            typeof(bool),
            typeof(DialogWindowBase),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示确定按钮依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowOkButtonProperty =
        DependencyProperty.Register(
            nameof(ShowOkButton),
            typeof(bool),
            typeof(DialogWindowBase),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示取消按钮依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowCancelButtonProperty =
        DependencyProperty.Register(
            nameof(ShowCancelButton),
            typeof(bool),
            typeof(DialogWindowBase),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否启用显示动画依赖属性
    /// </summary>
    public static readonly DependencyProperty EnableShowAnimationProperty =
        DependencyProperty.Register(
            nameof(EnableShowAnimation),
            typeof(bool),
            typeof(DialogWindowBase),
            new PropertyMetadata(true));

    #endregion

    #region 属性

    /// <summary>
    /// 对话框标题
    /// </summary>
    public string DialogTitle
    {
        get => (string)GetValue(DialogTitleProperty);
        set => SetValue(DialogTitleProperty, value);
    }

    /// <summary>
    /// 对话框内容
    /// </summary>
    public object DialogContent
    {
        get => GetValue(DialogContentProperty);
        set => SetValue(DialogContentProperty, value);
    }

    /// <summary>
    /// 是否显示对话框标题
    /// </summary>
    public bool ShowDialogTitle
    {
        get => (bool)GetValue(ShowDialogTitleProperty);
        set => SetValue(ShowDialogTitleProperty, value);
    }

    /// <summary>
    /// 是否显示按钮
    /// </summary>
    public bool ShowButtons
    {
        get => (bool)GetValue(ShowButtonsProperty);
        set => SetValue(ShowButtonsProperty, value);
    }

    /// <summary>
    /// 是否显示确定按钮
    /// </summary>
    public bool ShowOkButton
    {
        get => (bool)GetValue(ShowOkButtonProperty);
        set => SetValue(ShowOkButtonProperty, value);
    }

    /// <summary>
    /// 是否显示取消按钮
    /// </summary>
    public bool ShowCancelButton
    {
        get => (bool)GetValue(ShowCancelButtonProperty);
        set => SetValue(ShowCancelButtonProperty, value);
    }

    /// <summary>
    /// 是否启用显示动画
    /// </summary>
    public bool EnableShowAnimation
    {
        get => (bool)GetValue(EnableShowAnimationProperty);
        set => SetValue(EnableShowAnimationProperty, value);
    }

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 DialogWindowBase 类的新实例
    /// </summary>
    public DialogWindowBase()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        if (EnableShowAnimation && Resources["ShowAnimation"] is Storyboard showAnimation)
        {
            showAnimation.Begin();
        }
    }

    /// <summary>
    /// 确定按钮点击事件处理
    /// </summary>
    protected virtual void OkButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = true;
        Close();
    }

    /// <summary>
    /// 取消按钮点击事件处理
    /// </summary>
    protected virtual void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    #endregion

    #region 虚方法

    /// <summary>
    /// 验证对话框数据
    /// </summary>
    /// <returns>如果数据有效则返回true</returns>
    protected virtual bool ValidateData()
    {
        return true;
    }

    /// <summary>
    /// 在对话框关闭前调用
    /// </summary>
    /// <returns>如果允许关闭则返回true</returns>
    protected virtual bool OnClosing()
    {
        return true;
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 重写关闭事件
    /// </summary>
    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        if (!OnClosing())
        {
            e.Cancel = true;
            return;
        }

        base.OnClosing(e);
    }

    #endregion
}
