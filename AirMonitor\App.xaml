﻿<Application
    x:Class="AirMonitor.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:local="clr-namespace:AirMonitor"
    StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Styles/MainWindowStyles.xaml" />
                <ResourceDictionary Source="/Styles/FrameDataListenerStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToBrushConverter x:Key="BooleanToBrushConverter" />
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:NullToBooleanConverter x:Key="NullToBooleanConverter" />
            <converters:StringEmptyToBooleanConverter x:Key="StringEmptyToBooleanConverter" />
        </ResourceDictionary>

    </Application.Resources>
</Application>
