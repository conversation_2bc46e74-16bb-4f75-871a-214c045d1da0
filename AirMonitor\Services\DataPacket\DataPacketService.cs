using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using AirMonitor.Models;
using AirMonitor.Helpers;

namespace AirMonitor.Services;

/// <summary>
/// 数据分包器服务实现
/// </summary>
public class DataPacketService : IDataPacketService, IDisposable
{
    #region 字段

    private readonly ILogger<DataPacketService> _logger;
    private readonly object _bufferLock = new();
    private readonly List<byte> _buffer = new();
    private DataPacketProtocolType _protocolType = DataPacketProtocolType.AutoDetect;
    private bool _autoDetectProtocol = true;
    private long _totalParsedPackets = 0;
    private long _totalParseErrors = 0;
    private bool _disposed = false;

    // 协议头码常量
    private const byte COMMERCIAL_HEADER = 0x7E;
    private const byte MODULE_HEADER = 0x5B;

    // 缓冲区最大大小（防止内存泄漏）
    private const int MAX_BUFFER_SIZE = 4096;

    #endregion

    #region 事件

    public event EventHandler<DataPacketParsedEventArgs>? PacketParsed;
    public event EventHandler<DataPacketParseErrorEventArgs>? ParseError;

    #endregion

    #region 属性

    public DataPacketProtocolType ProtocolType => _protocolType;
    public bool AutoDetectProtocol 
    { 
        get => _autoDetectProtocol; 
        set => _autoDetectProtocol = value; 
    }
    public int BufferSize => MAX_BUFFER_SIZE;
    public int BufferedDataLength 
    { 
        get 
        { 
            lock (_bufferLock) 
            { 
                return _buffer.Count; 
            } 
        } 
    }
    public long TotalParsedPackets => _totalParsedPackets;
    public long TotalParseErrors => _totalParseErrors;

    #endregion

    #region 构造函数

    public DataPacketService(ILogger<DataPacketService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _logger.LogDebug("数据分包器服务已初始化");
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 设置协议类型
    /// </summary>
    public void SetProtocolType(DataPacketProtocolType protocolType)
    {
        _protocolType = protocolType;
        _autoDetectProtocol = protocolType == DataPacketProtocolType.AutoDetect;
        
        _logger.LogInformation("协议类型已设置为: {ProtocolType}, 自动检测: {AutoDetect}", 
            protocolType, _autoDetectProtocol);
    }

    /// <summary>
    /// 处理接收到的数据
    /// </summary>
    public async Task<List<DataPacketParseResult>> ProcessDataAsync(byte[] data, string portName = "")
    {
        if (data == null || data.Length == 0)
            return new List<DataPacketParseResult>();

        var results = new List<DataPacketParseResult>();

        try
        {
            _logger.LogTrace("开始处理数据: {Length} 字节, 端口: {PortName}", data.Length, portName);

            lock (_bufferLock)
            {
                // 添加数据到缓冲区
                _buffer.AddRange(data);

                // 检查缓冲区大小，防止内存泄漏
                if (_buffer.Count > MAX_BUFFER_SIZE)
                {
                    _logger.LogWarning("缓冲区超过最大大小，清空缓冲区");
                    _buffer.Clear();
                    OnParseError("缓冲区溢出", null, portName, data);
                    return results;
                }
            }

            // 解析数据包
            await Task.Run(() =>
            {
                while (true)
                {
                    var result = TryParseNextPacket(portName);
                    if (result == null)
                        break;

                    results.Add(result);

                    if (result.IsSuccess)
                    {
                        Interlocked.Increment(ref _totalParsedPackets);
                        OnPacketParsed(result, portName);
                        _logger.LogDebug("成功解析数据包: {ProtocolType}", result.Packet?.ProtocolType);
                    }
                    else
                    {
                        Interlocked.Increment(ref _totalParseErrors);
                        OnParseError(result.ErrorMessage ?? "未知错误", null, portName, Array.Empty<byte>());
                    }
                }
            });

            _logger.LogTrace("数据处理完成，解析出 {Count} 个数据包", results.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理数据时发生异常");
            OnParseError("数据处理异常", ex, portName, data);
        }

        return results;
    }

    /// <summary>
    /// 处理单个数据字节
    /// </summary>
    public async Task<DataPacketParseResult?> ProcessByteAsync(byte dataByte, string portName = "")
    {
        try
        {
            lock (_bufferLock)
            {
                _buffer.Add(dataByte);

                // 检查缓冲区大小
                if (_buffer.Count > MAX_BUFFER_SIZE)
                {
                    _logger.LogWarning("缓冲区超过最大大小，清空缓冲区");
                    _buffer.Clear();
                    OnParseError("缓冲区溢出", null, portName, new[] { dataByte });
                    return null;
                }
            }

            return await Task.Run(() => TryParseNextPacket(portName));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理字节时发生异常: 0x{Byte:X2}", dataByte);
            OnParseError("字节处理异常", ex, portName, new[] { dataByte });
            return null;
        }
    }

    /// <summary>
    /// 清空缓冲区
    /// </summary>
    public void ClearBuffer()
    {
        lock (_bufferLock)
        {
            var count = _buffer.Count;
            _buffer.Clear();
            _logger.LogDebug("缓冲区已清空，清除了 {Count} 字节", count);
        }
    }

    /// <summary>
    /// 获取缓冲区状态信息
    /// </summary>
    public string GetBufferStatus()
    {
        lock (_bufferLock)
        {
            return $"缓冲区: {_buffer.Count}/{MAX_BUFFER_SIZE} 字节";
        }
    }

    /// <summary>
    /// 验证数据包的CRC校验
    /// </summary>
    public bool ValidatePacketCrc(DataPacketBase packet)
    {
        try
        {
            return packet.ValidateCrc();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证CRC校验时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 尝试从原始数据解析数据包
    /// </summary>
    public DataPacketParseResult TryParsePacket(byte[] rawData, DataPacketProtocolType protocolType)
    {
        try
        {
            return protocolType switch
            {
                DataPacketProtocolType.CommercialProtocol => ParseCommercialProtocolPacket(rawData),
                DataPacketProtocolType.ModuleProtocol => ParseModuleProtocolPacket(rawData),
                _ => new DataPacketParseResult
                {
                    Status = DataPacketParseStatus.ParseError,
                    ErrorMessage = $"不支持的协议类型: {protocolType}"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析数据包时发生异常");
            return new DataPacketParseResult
            {
                Status = DataPacketParseStatus.ParseError,
                ErrorMessage = $"解析异常: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 检测数据的协议类型
    /// </summary>
    public DataPacketProtocolType? DetectProtocolType(byte[] data)
    {
        if (data == null || data.Length == 0)
            return null;

        var header = data[0];
        return header switch
        {
            COMMERCIAL_HEADER => DataPacketProtocolType.CommercialProtocol,
            MODULE_HEADER => DataPacketProtocolType.ModuleProtocol,
            _ => null
        };
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public Dictionary<string, object> GetStatistics()
    {
        return new Dictionary<string, object>
        {
            ["TotalParsedPackets"] = _totalParsedPackets,
            ["TotalParseErrors"] = _totalParseErrors,
            ["BufferedDataLength"] = BufferedDataLength,
            ["ProtocolType"] = _protocolType.ToString(),
            ["AutoDetectProtocol"] = _autoDetectProtocol
        };
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void ResetStatistics()
    {
        Interlocked.Exchange(ref _totalParsedPackets, 0);
        Interlocked.Exchange(ref _totalParseErrors, 0);
        _logger.LogDebug("统计信息已重置");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 尝试解析下一个数据包
    /// </summary>
    private DataPacketParseResult? TryParseNextPacket(string portName)
    {
        lock (_bufferLock)
        {
            if (_buffer.Count == 0)
                return null;

            // 查找头码
            int headerIndex = FindNextHeader();
            if (headerIndex == -1)
            {
                // 没有找到头码，清空缓冲区中的无效数据
                if (_buffer.Count > 0)
                {
                    _logger.LogTrace("未找到有效头码，清空 {Count} 字节无效数据", _buffer.Count);
                    _buffer.Clear();
                }
                return null;
            }

            // 移除头码之前的无效数据
            if (headerIndex > 0)
            {
                _logger.LogTrace("移除头码前的 {Count} 字节无效数据", headerIndex);
                _buffer.RemoveRange(0, headerIndex);
            }

            // 检测协议类型
            var detectedProtocol = DetectProtocolFromBuffer();
            if (detectedProtocol == null)
                return null;

            // 检查是否有足够的数据来解析数据包
            var minPacketLength = GetMinimumPacketLength(detectedProtocol.Value);
            if (_buffer.Count < minPacketLength)
                return null;

            // 尝试解析数据包
            var packetLength = GetPacketLength(detectedProtocol.Value);
            if (packetLength == -1 || _buffer.Count < packetLength)
                return null;

            // 提取数据包数据
            var packetData = new byte[packetLength];
            _buffer.CopyTo(0, packetData, 0, packetLength);

            // 解析数据包
            var result = TryParsePacket(packetData, detectedProtocol.Value);

            if (result.IsSuccess)
            {
                // 从缓冲区移除已解析的数据
                _buffer.RemoveRange(0, packetLength);
                result.ProcessedBytes = packetLength;
            }
            else
            {
                // 解析失败，移除第一个字节继续尝试
                _buffer.RemoveAt(0);
                result.ProcessedBytes = 1;
            }

            return result;
        }
    }

    /// <summary>
    /// 查找下一个有效头码的位置
    /// </summary>
    private int FindNextHeader()
    {
        for (int i = 0; i < _buffer.Count; i++)
        {
            if (_buffer[i] == COMMERCIAL_HEADER || _buffer[i] == MODULE_HEADER)
            {
                return i;
            }
        }
        return -1;
    }

    /// <summary>
    /// 从缓冲区检测协议类型
    /// </summary>
    private DataPacketProtocolType? DetectProtocolFromBuffer()
    {
        if (_buffer.Count == 0)
            return null;

        if (_autoDetectProtocol)
        {
            return DetectProtocolType(_buffer.ToArray());
        }
        else
        {
            // 验证指定的协议类型是否匹配
            var expectedHeader = _protocolType switch
            {
                DataPacketProtocolType.CommercialProtocol => COMMERCIAL_HEADER,
                DataPacketProtocolType.ModuleProtocol => MODULE_HEADER,
                _ => (byte)0
            };

            return _buffer[0] == expectedHeader ? _protocolType : null;
        }
    }

    /// <summary>
    /// 获取协议的最小数据包长度
    /// </summary>
    private int GetMinimumPacketLength(DataPacketProtocolType protocolType)
    {
        return protocolType switch
        {
            DataPacketProtocolType.CommercialProtocol => 7, // 头码+源地址+目标地址+命令码+长度+CRC(2字节)
            DataPacketProtocolType.ModuleProtocol => 6,     // 头码+地址+命令码+长度+CRC(2字节)
            _ => 0
        };
    }

    /// <summary>
    /// 获取数据包的实际长度
    /// </summary>
    private int GetPacketLength(DataPacketProtocolType protocolType)
    {
        if (_buffer.Count < 5)
            return -1;

        return protocolType switch
        {
            DataPacketProtocolType.CommercialProtocol => GetCommercialPacketLength(),
            DataPacketProtocolType.ModuleProtocol => GetModulePacketLength(),
            _ => -1
        };
    }

    /// <summary>
    /// 获取商用协议数据包长度
    /// </summary>
    private int GetCommercialPacketLength()
    {
        if (_buffer.Count < 5)
            return -1;

        // 商用协议：报文长度字段表示从头码到CRC校验结束的总长度
        var messageLength = _buffer[4];
        return messageLength;
    }

    /// <summary>
    /// 获取模块协议数据包长度
    /// </summary>
    private int GetModulePacketLength()
    {
        if (_buffer.Count < 4)
            return -1;

        // 模块协议：报文长度字段表示除CRC校验外的数据长度
        var messageLength = _buffer[3];
        return messageLength + 2; // 加上CRC校验的2字节
    }

    /// <summary>
    /// 解析商用协议数据包
    /// </summary>
    private DataPacketParseResult ParseCommercialProtocolPacket(byte[] rawData)
    {
        try
        {
            if (rawData.Length < 7)
            {
                return new DataPacketParseResult
                {
                    Status = DataPacketParseStatus.ParseError,
                    ErrorMessage = "商用协议数据包长度不足"
                };
            }

            var packet = new CommercialProtocolPacket
            {
                RawData = rawData,
                Header = rawData[0],
                SourceAddress = rawData[1],
                TargetAddress = rawData[2],
                CommandCode = rawData[3],
                MessageLength = rawData[4]
            };

            // 提取数据字
            var dataLength = rawData.Length - 7; // 总长度 - 固定字段长度(5) - CRC长度(2)
            if (dataLength > 0)
            {
                packet.DataBytes = new byte[dataLength];
                Array.Copy(rawData, 5, packet.DataBytes, 0, dataLength);
            }

            // 验证CRC
            packet.ValidateCrc();

            return new DataPacketParseResult
            {
                Status = DataPacketParseStatus.ParseCompleted,
                Packet = packet,
                ProcessedBytes = rawData.Length
            };
        }
        catch (Exception ex)
        {
            return new DataPacketParseResult
            {
                Status = DataPacketParseStatus.ParseError,
                ErrorMessage = $"解析商用协议数据包失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 解析模块协议数据包
    /// </summary>
    private DataPacketParseResult ParseModuleProtocolPacket(byte[] rawData)
    {
        try
        {
            if (rawData.Length < 6)
            {
                return new DataPacketParseResult
                {
                    Status = DataPacketParseStatus.ParseError,
                    ErrorMessage = "模块协议数据包长度不足"
                };
            }

            var packet = new ModuleProtocolPacket
            {
                RawData = rawData,
                Header = rawData[0],
                Address = rawData[1],
                CommandCode = rawData[2],
                MessageLength = rawData[3]
            };

            // 提取数据字
            var dataLength = rawData.Length - 6; // 总长度 - 固定字段长度(4) - CRC长度(2)
            if (dataLength > 0)
            {
                packet.DataBytes = new byte[dataLength];
                Array.Copy(rawData, 4, packet.DataBytes, 0, dataLength);
            }

            // 验证CRC
            packet.ValidateCrc();

            return new DataPacketParseResult
            {
                Status = DataPacketParseStatus.ParseCompleted,
                Packet = packet,
                ProcessedBytes = rawData.Length
            };
        }
        catch (Exception ex)
        {
            return new DataPacketParseResult
            {
                Status = DataPacketParseStatus.ParseError,
                ErrorMessage = $"解析模块协议数据包失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 触发数据包解析完成事件
    /// </summary>
    private void OnPacketParsed(DataPacketParseResult result, string portName)
    {
        try
        {
            PacketParsed?.Invoke(this, new DataPacketParsedEventArgs
            {
                Result = result,
                PortName = portName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发PacketParsed事件时发生异常");
        }
    }

    /// <summary>
    /// 触发解析错误事件
    /// </summary>
    private void OnParseError(string errorMessage, Exception? exception, string portName, byte[] rawData)
    {
        try
        {
            ParseError?.Invoke(this, new DataPacketParseErrorEventArgs
            {
                ErrorMessage = errorMessage,
                Exception = exception,
                PortName = portName,
                RawData = rawData
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "触发ParseError事件时发生异常");
        }
    }

    #endregion

    #region IDisposable 实现

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                ClearBuffer();
                _logger.LogDebug("数据分包器服务已释放");
            }
            _disposed = true;
        }
    }

    #endregion
}
