namespace AirMonitor.Services;

/// <summary>
/// 日志服务接口
/// </summary>
public interface ILoggingService
{
    /// <summary>
    /// 记录详细信息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogVerbose(string message, params object[] args);

    /// <summary>
    /// 记录调试信息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录一般信息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告信息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误信息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogError(string message, params object[] args);

    /// <summary>
    /// 记录错误信息（带异常）
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogError(Exception exception, string message, params object[] args);

    /// <summary>
    /// 记录致命错误
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogFatal(string message, params object[] args);

    /// <summary>
    /// 记录致命错误（带异常）
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogFatal(Exception exception, string message, params object[] args);

    /// <summary>
    /// 开始性能计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>性能计时器</returns>
    IDisposable BeginScope(string operationName);

    /// <summary>
    /// 刷新日志缓冲区
    /// </summary>
    void Flush();
}
