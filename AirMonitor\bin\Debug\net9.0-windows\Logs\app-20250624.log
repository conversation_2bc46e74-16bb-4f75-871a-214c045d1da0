2025-06-24 09:25:38.433 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:25:38.457 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:25:38.457 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:25:38.458 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:25:38.458 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:25:38.467 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:25:38.467 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:25:39.371 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:25:39.373 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:25:44.878 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:25:44.879 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:25:45.805 +08:00 [INF] [1] : 显示关于信息
2025-06-24 09:25:58.061 +08:00 [INF] [1] : 用户请求退出应用程序
2025-06-24 09:25:58.074 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 09:26:01.683 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:26:01.711 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:26:01.711 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:26:01.712 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:26:01.713 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:26:01.725 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:26:01.725 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:26:02.434 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:26:02.436 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:26:04.430 +08:00 [INF] [1] : 执行示例测试命令
2025-06-24 09:26:07.628 +08:00 [INF] [1] : 执行示例测试命令
2025-06-24 09:26:11.820 +08:00 [INF] [1] : 执行示例测试命令
2025-06-24 09:26:16.029 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 09:52:34.633 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:52:34.658 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:52:34.658 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:52:34.659 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:52:34.659 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:52:34.670 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:52:34.670 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:52:35.399 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:52:35.401 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:52:39.375 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 09:52:48.054 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 09:52:53.134 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 09:52:55.254 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:55:00.429 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:55:00.459 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:55:00.459 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:55:00.460 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:55:00.460 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:55:00.472 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:55:00.472 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:55:01.145 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:55:01.148 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:55:05.047 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 09:55:18.712 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 09:55:22.861 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 09:55:24.462 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:55:49.496 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:55:49.520 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:55:49.520 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:55:49.521 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:55:49.521 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:55:49.532 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:55:49.532 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:55:50.290 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:55:50.291 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:55:53.638 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:58:35.179 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 09:58:35.203 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 09:58:35.204 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 09:58:35.204 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 09:58:35.204 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 09:58:35.224 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 09:58:35.225 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 09:58:36.308 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 09:58:36.309 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 09:58:40.814 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:58:43.246 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:58:44.997 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 09:58:51.798 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:00:03.298 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:00:03.322 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:00:03.322 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:00:03.323 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:00:03.323 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:00:03.329 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:00:03.329 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:00:04.207 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:00:04.209 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:00:06.999 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:00:14.878 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:00:16.976 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 10:00:19.271 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 10:00:21.271 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:00:23.727 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 10:03:28.646 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:03:28.671 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:03:28.671 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:03:28.672 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:03:28.672 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:03:28.678 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:03:28.679 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:03:29.470 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:03:29.472 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:03:32.775 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:03:36.258 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:03:41.448 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 10:05:49.032 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:05:49.055 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:05:49.056 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:05:49.056 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:05:49.057 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:05:49.064 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:05:49.064 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:05:49.717 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:05:49.719 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:05:53.919 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:05:58.511 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:06:19.329 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:06:19.349 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:06:19.349 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:06:19.350 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:06:19.350 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:06:19.356 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:06:19.356 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:06:20.000 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:06:20.002 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:06:21.203 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:06:26.092 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:06:28.083 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 10:06:33.419 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:06:48.717 +08:00 [INF] [1] : 显示简单对话框
2025-06-24 10:06:54.777 +08:00 [INF] [1] : 显示非模态窗口
2025-06-24 10:06:56.717 +08:00 [INF] [1] : 显示示例对话框
2025-06-24 10:20:46.494 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:20:46.521 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:20:46.521 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:20:46.522 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:20:46.522 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:20:46.535 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:20:46.535 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:23:14.107 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:23:14.129 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:23:14.130 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:23:14.130 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:23:14.130 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:23:14.138 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:23:14.139 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:24:32.563 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:24:32.587 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:24:32.587 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:24:32.588 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:24:32.588 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:24:32.596 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:24:32.596 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:24:33.487 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:24:33.489 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:24:36.029 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:24:39.162 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 10:24:43.464 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:24:43.486 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:24:43.486 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:24:43.487 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:24:43.487 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:24:43.496 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:24:43.496 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:24:44.190 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:24:44.192 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:24:48.127 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:24:51.622 +08:00 [INF] [1] : 显示关于信息
2025-06-24 10:25:02.589 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:42:53.697 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:42:53.721 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:42:53.721 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:42:53.722 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:42:53.722 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:42:53.730 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:42:53.731 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:42:54.805 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:42:54.808 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:42:55.855 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:42:58.662 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:43:45.399 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:43:45.425 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:43:45.425 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:43:45.426 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:43:45.426 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:43:45.463 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:43:45.463 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:43:46.323 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:43:46.325 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:43:48.283 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:48:58.184 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:48:58.210 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:48:58.211 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:48:58.211 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:48:58.211 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:48:58.219 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:48:58.219 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:48:58.894 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:48:58.896 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:49:00.033 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:49:01.136 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:50:16.166 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:50:16.190 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:50:16.190 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:50:16.191 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:50:16.191 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:50:16.203 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:50:16.203 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:50:17.269 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:50:17.271 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:50:18.584 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:50:20.812 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 10:51:11.009 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:51:11.035 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:51:11.036 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:51:11.036 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:51:11.036 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:51:11.046 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:51:11.046 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:51:12.017 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:51:12.019 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:51:13.048 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:51:13.969 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:51:14.057 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 10:51:15.068 +08:00 [INF] [1] : 串口连接对话框初始化完成
2025-06-24 10:54:43.290 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 10:54:43.314 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 10:54:43.315 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 10:54:43.315 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 10:54:43.316 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 10:54:43.323 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 10:54:43.323 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 10:54:44.025 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 10:54:44.027 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 10:54:45.435 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 10:54:46.600 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 10:54:46.698 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 10:54:47.417 +08:00 [INF] [1] : 串口连接对话框初始化完成
2025-06-24 11:02:22.412 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 11:02:22.437 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 11:02:22.437 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 11:02:22.438 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 11:02:22.438 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 11:02:22.450 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 11:02:22.450 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 11:02:23.576 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 11:02:23.578 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 11:02:25.873 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:02:25.962 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:02:26.688 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 11:02:36.797 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:05:27.575 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 11:05:27.601 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 11:05:27.601 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 11:05:27.602 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 11:05:27.602 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 11:05:27.612 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 11:05:27.613 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 11:05:28.459 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 11:05:28.461 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 11:05:29.934 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:05:30.028 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:05:30.770 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 11:05:42.776 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 11:05:42.818 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 11:05:45.729 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:12:51.802 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 11:12:51.828 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 11:12:51.828 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 11:12:51.829 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 11:12:51.829 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 11:12:51.842 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 11:12:51.842 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 11:12:52.837 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 11:12:52.839 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 11:12:56.457 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:12:56.536 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:12:57.223 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 11:13:02.837 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 11:13:02.891 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 11:13:09.366 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:13:11.533 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:13:11.591 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:13:12.241 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Connected"
2025-06-24 11:13:14.748 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接已断开: COM11
2025-06-24 11:13:14.752 +08:00 [INF] [1] : 串口连接已断开并已保存配置
2025-06-24 11:13:15.717 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:13:20.953 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:13:20.986 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:13:21.586 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 11:13:24.737 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM6, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 11:13:24.738 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM6
2025-06-24 11:13:52.460 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:13:53.696 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:13:53.725 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:13:54.360 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Connected"
2025-06-24 11:16:43.663 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 11:16:43.685 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 11:16:43.686 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 11:16:43.687 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 11:16:43.687 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 11:16:43.703 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 11:16:43.703 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 11:16:44.672 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 11:16:44.674 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 11:16:46.307 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 11:16:46.399 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 11:16:47.109 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 11:16:50.834 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM6, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 11:16:50.880 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM6
2025-06-24 11:17:06.003 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 11:18:12.542 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 13:19:05.011 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 13:19:05.059 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 13:19:05.059 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 13:19:05.061 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 13:19:05.061 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 13:19:05.069 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 13:19:05.069 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 13:19:06.263 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 13:19:06.265 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 13:42:17.518 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 13:42:17.541 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 13:42:17.541 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 13:42:17.542 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 13:42:17.542 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 13:42:17.549 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 13:42:17.549 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 13:42:18.290 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 13:42:18.292 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 13:42:21.355 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 13:42:21.444 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 13:42:21.449 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 13:42:25.998 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 13:42:28.008 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 13:42:28.064 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 13:42:28.066 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 13:42:32.440 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 13:42:32.491 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 13:42:33.396 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 13:42:33.401 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 13:42:33.401 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 13:42:36.810 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 13:42:36.810 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 13:42:36.904 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 13:42:36.904 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 13:42:36.906 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 13:42:36.951 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 13:42:37.935 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 13:43:26.589 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 13:49:48.391 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 13:49:48.420 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 13:49:48.420 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 13:49:48.421 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 13:49:48.421 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 13:49:48.437 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 13:49:48.437 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 13:49:48.443 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 13:49:49.176 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 13:49:49.178 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 13:51:25.395 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 13:51:25.464 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 13:51:26.355 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 13:51:26.359 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 13:51:26.359 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 13:51:30.025 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 13:51:30.025 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 13:51:30.120 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 13:51:30.120 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 13:51:30.120 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:00:03.817 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:00:03.840 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:00:03.840 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:00:03.841 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:00:03.841 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:00:03.848 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:00:03.848 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:00:03.855 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:00:04.614 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:00:04.616 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:00:07.150 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:00:07.219 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:00:07.931 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:00:07.935 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:00:07.936 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:00:11.161 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:00:11.162 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:00:11.257 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:00:11.258 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:00:11.258 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:00:15.916 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 14:00:18.123 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:00:20.818 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:00:20.879 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:00:20.881 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:00:52.658 +08:00 [INF] [1] : 帧数据监听已暂停
2025-06-24 14:02:09.531 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:02:09.557 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:02:09.557 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:02:09.558 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:02:09.558 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:02:09.570 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:02:09.570 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:02:09.575 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:02:10.431 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:02:10.433 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:02:12.117 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:02:12.227 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:02:12.927 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:02:12.931 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:02:12.931 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:02:17.699 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:02:17.700 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:02:17.796 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:02:17.796 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:02:17.796 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:06:09.626 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:06:09.650 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:06:09.651 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:06:09.652 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:06:09.652 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:06:09.663 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:06:09.663 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:06:09.667 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:06:10.432 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:06:10.433 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:06:12.030 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 14:06:12.446 +08:00 [INF] [1] : 工具菜单被点击
2025-06-24 14:06:13.919 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:06:13.982 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:06:14.679 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:06:14.684 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:06:14.684 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:06:18.013 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:06:18.014 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:06:18.260 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM6, 错误: 无
2025-06-24 14:06:18.261 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:06:18.261 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM6, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:06:18.304 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM6
2025-06-24 14:06:20.331 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:06:21.529 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:06:21.624 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:06:21.626 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:06:23.636 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 14:06:25.110 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:06:25.140 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:06:25.612 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:06:25.613 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:06:25.613 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Connected"
2025-06-24 14:06:27.215 +08:00 [INF] [1] : 开始断开串口连接...
2025-06-24 14:06:27.216 +08:00 [INF] [1] : 收到连接状态变化事件: "Connected" -> "Disconnecting", 串口: COM6, 错误: 无
2025-06-24 14:06:27.216 +08:00 [INF] [1] : 连接状态更新完成: "Disconnecting"
2025-06-24 14:06:27.216 +08:00 [INF] [1] : 收到连接状态变化事件: "Connected" -> "Disconnecting", 串口: COM6, 错误: 无
2025-06-24 14:06:27.216 +08:00 [INF] [1] : 连接状态更新完成: "Disconnecting"
2025-06-24 14:06:27.327 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnecting" -> "Disconnected", 串口: COM6, 错误: 无
2025-06-24 14:06:27.327 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:06:27.327 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnecting" -> "Disconnected", 串口: COM6, 错误: 无
2025-06-24 14:06:27.327 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:06:27.328 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接已断开: COM6
2025-06-24 14:06:27.328 +08:00 [INF] [1] : 串口断开连接结果: true
2025-06-24 14:06:27.328 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:06:27.331 +08:00 [INF] [1] : 串口连接已断开并已保存配置，UI状态已更新
2025-06-24 14:06:29.532 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: COM6, 错误: 无
2025-06-24 14:06:29.532 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:06:29.532 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: COM6, 错误: 无
2025-06-24 14:06:29.532 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:06:29.548 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:06:29.550 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:06:29.550 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:06:29.550 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:06:29.550 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:06:29.552 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 14:06:31.160 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:06:32.382 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:06:32.433 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:06:32.435 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:06:53.294 +08:00 [INF] [1] : 帧数据监听已暂停
2025-06-24 14:06:53.926 +08:00 [INF] [1] : 帧数据监听已继续
2025-06-24 14:06:55.117 +08:00 [INF] [1] : 帧数据监听已暂停
2025-06-24 14:06:55.597 +08:00 [INF] [1] : 帧数据监听已继续
2025-06-24 14:07:01.615 +08:00 [INF] [10] AirMonitor.Services.SerialPortService: 检测到串口列表变化 - 新增: [], 移除: [COM6]
2025-06-24 14:07:01.955 +08:00 [INF] [5] : 收到串口列表变化事件 - 新增: [], 移除: [COM6], 可用串口总数: 2
2025-06-24 14:07:01.966 +08:00 [INF] [5] : 收到串口列表变化事件 - 新增: [], 移除: [COM6], 可用串口总数: 2
2025-06-24 14:07:02.390 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 2, 当前选择: 无
2025-06-24 14:07:02.390 +08:00 [INF] [1] : 串口已从UI列表移除: COM6
2025-06-24 14:07:02.391 +08:00 [INF] [1] : UI线程串口列表变化处理完成 - 当前UI列表包含 2 个串口
2025-06-24 14:07:02.391 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 2, 当前选择: COM11
2025-06-24 14:07:02.391 +08:00 [INF] [1] : 串口已从UI列表移除: COM6
2025-06-24 14:07:02.391 +08:00 [INF] [1] : UI线程串口列表变化处理完成 - 当前UI列表包含 2 个串口
2025-06-24 14:07:05.616 +08:00 [INF] [19] AirMonitor.Services.SerialPortService: 检测到串口列表变化 - 新增: [COM6], 移除: []
2025-06-24 14:07:06.075 +08:00 [INF] [10] : 收到串口列表变化事件 - 新增: [COM6], 移除: [], 可用串口总数: 3
2025-06-24 14:07:06.075 +08:00 [INF] [10] : 收到串口列表变化事件 - 新增: [COM6], 移除: [], 可用串口总数: 3
2025-06-24 14:07:06.751 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:07:06.751 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: COM11
2025-06-24 14:07:06.751 +08:00 [INF] [1] : 新串口已添加到UI列表: COM6
2025-06-24 14:07:06.751 +08:00 [INF] [1] : UI线程串口列表变化处理完成 - 当前UI列表包含 3 个串口
2025-06-24 14:07:06.751 +08:00 [INF] [1] : 新串口已添加到UI列表: COM6
2025-06-24 14:07:06.751 +08:00 [INF] [1] : UI线程串口列表变化处理完成 - 当前UI列表包含 3 个串口
2025-06-24 14:07:22.681 +08:00 [INF] [1] : 已清空 10 条帧数据显示
2025-06-24 14:07:24.916 +08:00 [INF] [1] : 帧数据监听已暂停
2025-06-24 14:07:28.495 +08:00 [ERR] [1] : 复制数据到剪贴板失败
System.Runtime.InteropServices.COMException (0x800401D0): OpenClipboard 失败 (0x800401D0 (CLIPBRD_E_CANT_OPEN))
   at System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(Int32 errorCode, IntPtr errorInfo)
   at System.Windows.Clipboard.Flush()
   at AirMonitor.ViewModels.FrameDataListenerViewModel.CopySelected() in D:\00 AirMonitor\AirMonitor\ViewModels\FrameDataListenerViewModel.cs:line 273
2025-06-24 14:08:11.755 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:08:11.778 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:08:11.779 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:08:11.779 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:08:11.780 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:08:11.789 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:08:11.789 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:08:11.793 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:08:12.617 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:08:12.619 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:08:13.735 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:08:13.836 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:08:14.578 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:08:14.583 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:08:14.583 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:08:17.523 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:08:17.524 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:08:17.626 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:08:17.626 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:08:17.626 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:08:17.672 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 14:08:18.345 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:08:20.171 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:08:20.229 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:08:20.232 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:17:06.683 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:17:06.720 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:17:06.721 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:17:06.723 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:17:06.724 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:17:06.734 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:17:06.734 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:17:06.739 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:17:07.378 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:17:07.380 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:17:11.173 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:17:11.233 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:17:11.929 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:17:11.934 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:17:11.934 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:17:12.464 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:17:13.640 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:17:13.705 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:17:13.708 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:17:23.796 +08:00 [INF] [1] : 串口连接菜单被点击
2025-06-24 14:17:23.838 +08:00 [INF] [1] : 正在初始化串口连接对话框...
2025-06-24 14:17:24.457 +08:00 [INF] [1] : 串口列表刷新完成 - 总数: 3, 当前选择: 无
2025-06-24 14:17:24.457 +08:00 [INF] [1] : 连接状态更新完成: "Disconnected"
2025-06-24 14:17:24.458 +08:00 [INF] [1] : 串口连接对话框初始化完成，当前状态: "Disconnected"
2025-06-24 14:17:33.467 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:17:33.468 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:17:33.468 +08:00 [INF] [1] : 收到连接状态变化事件: "Disconnected" -> "Connecting", 串口: , 错误: 无
2025-06-24 14:17:33.468 +08:00 [INF] [1] : 连接状态更新完成: "Connecting"
2025-06-24 14:17:33.572 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:17:33.572 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:17:33.572 +08:00 [INF] [1] : 收到连接状态变化事件: "Connecting" -> "Connected", 串口: COM11, 错误: 无
2025-06-24 14:17:33.572 +08:00 [INF] [1] : 连接状态更新完成: "Connected"
2025-06-24 14:17:33.572 +08:00 [INF] [1] AirMonitor.Services.SerialPortService: 串口连接成功: COM11, 协议: 商用内外机通讯协议, 波特率: 9600
2025-06-24 14:17:33.623 +08:00 [INF] [1] : 串口连接成功并已保存配置: COM11
2025-06-24 14:17:37.527 +08:00 [INF] [1] : 用户取消了串口连接配置
2025-06-24 14:17:42.299 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:17:42.331 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:17:42.332 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:17:46.545 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 14:20:32.346 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 14:20:34.404 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:20:34.447 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:20:34.450 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:20:38.010 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 14:20:46.075 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 14:20:46.124 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 14:20:46.125 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 14:20:50.010 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 14:55:45.683 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:55:45.705 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:55:45.706 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:55:45.706 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:55:45.707 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:55:45.714 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:55:45.714 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:55:45.718 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:55:46.593 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:55:46.595 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:55:48.916 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 14:55:48.924 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 14:55:48.927 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 14:55:48.930 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:55:49.063 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 14:55:49.065 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:55:49.066 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:55:49.067 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 14:55:49.070 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 14:55:54.914 +08:00 [INF] [1] : 开始创建新的协议解析配置
2025-06-24 14:56:47.400 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 14:56:47.428 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 14:56:47.429 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 14:56:47.430 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 14:56:47.430 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 14:56:47.444 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 14:56:47.444 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 14:56:47.449 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 14:56:48.364 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 14:56:48.366 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 14:56:49.974 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 14:56:49.979 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 14:56:49.982 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 14:56:50.006 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:56:50.130 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 14:56:50.132 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:56:50.132 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 14:56:50.134 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 14:56:50.138 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 14:56:56.064 +08:00 [INF] [1] : 用户尝试导入配置
2025-06-24 14:56:57.911 +08:00 [INF] [1] : 用户尝试导入配置
2025-06-24 14:56:58.663 +08:00 [INF] [1] : 用户尝试导入配置
2025-06-24 14:57:07.112 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 14:57:07.112 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:07:58.387 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:07:58.409 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:07:58.409 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:07:58.410 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:07:58.410 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:07:58.417 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:07:58.418 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:07:58.422 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:07:59.279 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:07:59.281 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:08:02.104 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:08:02.110 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:08:02.114 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 15:08:02.116 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:08:02.231 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个协议解析配置
2025-06-24 15:08:02.233 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:08:02.234 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:08:02.235 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:08:02.239 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:08:20.608 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置导入成功: 商用内外机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\Configs\ProtocolParseConfigs\example-commercial-protocol.json
2025-06-24 15:08:20.636 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: 商用内外机通讯协议示例配置 (1ca6a90a-9c35-49a0-8eab-baf02fa8ef3e)
2025-06-24 15:08:20.645 +08:00 [INF] [1] : 配置导入成功: 商用内外机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\Configs\ProtocolParseConfigs\example-commercial-protocol.json
2025-06-24 15:08:31.828 +08:00 [INF] [1] : 配置验证通过: 商用内外机通讯协议示例配置
2025-06-24 15:08:47.383 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:08:47.383 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:08:47.384 +08:00 [INF] [1] : 示例配置加载完成: 成功=0, 错误=2
2025-06-24 15:08:56.102 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置导入成功: 商用内外机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\Configs\ProtocolParseConfigs\example-commercial-protocol.json
2025-06-24 15:08:56.105 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: 商用内外机通讯协议示例配置 (1) (ba149640-84ae-454e-8a74-58dde9f4751a)
2025-06-24 15:08:56.114 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置导入成功: 模块机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\Configs\ProtocolParseConfigs\example-module-protocol.json
2025-06-24 15:08:56.127 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: 模块机通讯协议示例配置 (6f8dd247-b3c0-4771-b021-1fe196d8c664)
2025-06-24 15:08:56.128 +08:00 [INF] [1] : 批量导入配置完成: 成功=2, 失败=0
2025-06-24 15:09:03.537 +08:00 [ERR] [1] AirMonitor.Services.ProtocolParseConfigService: 导入配置时发生错误: D:\00 AirMonitor\AirMonitor\Configs\PhysicalQuantities\default-quantities.json
System.Text.Json.JsonException: The JSON value could not be converted to AirMonitor.Models.ProtocolParseConfig. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonException_DeserializeUnableToConvertValue(Type propertyType)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at AirMonitor.Services.ProtocolParseConfigService.ImportConfigFromFileAsync(String filePath) in D:\00 AirMonitor\AirMonitor\Services\ProtocolParse\ProtocolParseConfigService.cs:line 421
2025-06-24 15:09:03.565 +08:00 [WRN] [1] : 导入配置失败: D:\00 AirMonitor\AirMonitor\Configs\PhysicalQuantities\default-quantities.json
2025-06-24 15:09:10.352 +08:00 [ERR] [1] AirMonitor.Services.ProtocolParseConfigService: 导入配置时发生错误: D:\00 AirMonitor\AirMonitor\Configs\PhysicalQuantities\default-quantities.json
System.Text.Json.JsonException: The JSON value could not be converted to AirMonitor.Models.ProtocolParseConfig. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonException_DeserializeUnableToConvertValue(Type propertyType)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at AirMonitor.Services.ProtocolParseConfigService.ImportConfigFromFileAsync(String filePath) in D:\00 AirMonitor\AirMonitor\Services\ProtocolParse\ProtocolParseConfigService.cs:line 421
2025-06-24 15:09:10.353 +08:00 [INF] [1] : 批量导入配置完成: 成功=0, 失败=1
2025-06-24 15:09:13.297 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:09:13.297 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:09:44.043 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:09:44.065 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:09:44.065 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:09:44.066 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:09:44.066 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:09:44.073 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:09:44.073 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:09:44.078 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:09:44.791 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:09:44.793 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:09:46.339 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:09:46.350 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:09:46.467 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 3 个协议解析配置
2025-06-24 15:09:46.468 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:09:46.560 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:09:46.560 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:09:46.561 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:09:46.591 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:09:58.233 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 模块机通讯协议示例配置 (6f8dd247-b3c0-4771-b021-1fe196d8c664)
2025-06-24 15:09:58.235 +08:00 [INF] [1] : 协议解析配置删除成功: 模块机通讯协议示例配置
2025-06-24 15:09:59.844 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 商用内外机通讯协议示例配置 (1ca6a90a-9c35-49a0-8eab-baf02fa8ef3e)
2025-06-24 15:09:59.846 +08:00 [INF] [1] : 协议解析配置删除成功: 商用内外机通讯协议示例配置
2025-06-24 15:10:00.852 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 商用内外机通讯协议示例配置 (1) (ba149640-84ae-454e-8a74-58dde9f4751a)
2025-06-24 15:10:00.854 +08:00 [INF] [1] : 协议解析配置删除成功: 商用内外机通讯协议示例配置 (1)
2025-06-24 15:10:05.432 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:10:05.433 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:10:05.433 +08:00 [INF] [1] : 示例配置加载完成: 成功=0, 错误=2
2025-06-24 15:10:12.120 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:10:12.120 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:13:13.631 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:13:13.653 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:13:13.653 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:13:13.654 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:13:13.654 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:13:13.663 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:13:13.663 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:13:13.667 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:13:14.388 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:13:14.390 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:13:16.355 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:13:16.359 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:13:16.423 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 2 个协议解析配置
2025-06-24 15:13:16.425 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:13:16.491 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:13:16.576 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:13:19.592 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 模块机通讯协议示例配置 (example-module-001)
2025-06-24 15:13:19.596 +08:00 [INF] [1] : 协议解析配置删除成功: 模块机通讯协议示例配置
2025-06-24 15:13:21.358 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 商用内外机通讯协议示例配置 (example-commercial-001)
2025-06-24 15:13:21.360 +08:00 [INF] [1] : 协议解析配置删除成功: 商用内外机通讯协议示例配置
2025-06-24 15:13:24.432 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置导入成功: 商用内外机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows\Configs\ProtocolParseConfigs\example-commercial-protocol.json
2025-06-24 15:13:24.454 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: 商用内外机通讯协议示例配置 (a3837eb8-42f4-4f7b-a8cb-76823ebb8f3d)
2025-06-24 15:13:24.463 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置导入成功: 模块机通讯协议示例配置 <- D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows\Configs\ProtocolParseConfigs\example-module-protocol.json
2025-06-24 15:13:24.465 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: 模块机通讯协议示例配置 (7e785402-47bd-4247-8d23-2903bff393f2)
2025-06-24 15:13:24.468 +08:00 [INF] [1] : 示例配置加载完成: 成功=2, 错误=0
2025-06-24 15:13:41.728 +08:00 [INF] [1] : 开始编辑物理量定义: Voltage
2025-06-24 15:14:41.878 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:14:41.901 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:14:41.901 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:14:41.902 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:14:41.902 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:14:41.910 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:14:41.910 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:14:41.914 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:14:42.713 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:14:42.715 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:14:44.253 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:14:44.258 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:14:44.390 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 4 个协议解析配置
2025-06-24 15:14:44.392 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:14:44.472 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:14:44.605 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:15:00.887 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:15:00.887 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:25:44.919 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:25:44.943 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:25:44.943 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:25:44.944 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:25:44.944 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:25:44.955 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:25:44.955 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:25:44.959 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:25:45.744 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:25:45.746 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:25:47.596 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:25:47.601 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:28:29.345 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:28:29.368 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:28:29.368 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:28:29.369 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:28:29.369 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:28:29.380 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:28:29.380 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:28:29.393 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:28:30.227 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:28:30.229 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:28:31.849 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:28:31.854 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:28:31.965 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 4 个协议解析配置
2025-06-24 15:28:31.967 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:28:32.059 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:28:32.168 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:30:06.244 +08:00 [INF] [1] : 开始创建新的数据映射配置
2025-06-24 15:32:08.051 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:32:08.051 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:32:25.225 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:32:25.247 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:32:25.248 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:32:25.249 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:32:25.249 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:32:25.259 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:32:25.259 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:32:25.263 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:32:26.076 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:32:26.078 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:32:28.035 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:32:28.039 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:32:28.128 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 4 个协议解析配置
2025-06-24 15:32:28.131 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:32:28.228 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:32:28.344 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:32:33.546 +08:00 [INF] [1] : 开始创建新的协议解析配置
2025-06-24 15:33:15.698 +08:00 [INF] [1] : 开始创建新的协议解析配置
2025-06-24 15:33:42.030 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: A1帧配置 (96bc5af9-9fb3-4bc5-9068-1052eeef82e1)
2025-06-24 15:33:42.032 +08:00 [INF] [1] : 协议解析配置保存成功: A1帧配置
2025-06-24 15:33:57.247 +08:00 [INF] [1] : 开始编辑协议解析配置: 模块机通讯协议示例配置
2025-06-24 15:34:07.692 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: A1帧配置 (96bc5af9-9fb3-4bc5-9068-1052eeef82e1)
2025-06-24 15:34:07.692 +08:00 [INF] [1] : 协议解析配置保存成功: A1帧配置
2025-06-24 15:34:11.627 +08:00 [INF] [1] : 开始创建新的物理量定义
2025-06-24 15:34:17.030 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:34:17.033 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:34:22.463 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:34:22.467 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:34:24.612 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:34:24.613 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:34:25.673 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:34:25.675 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:34:26.816 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:34:26.821 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:34:27.967 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:34:27.968 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:34:30.356 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:34:30.357 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:34:31.434 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:34:31.436 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:34:32.345 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:34:32.346 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:34:33.582 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:34:33.584 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:34:39.887 +08:00 [INF] [1] : 开始创建新的物理量定义
2025-06-24 15:38:13.821 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: IndoorTempFixFlag
2025-06-24 15:38:39.786 +08:00 [ERR] [1] : ViewModel操作出错: 保存物理量
System.NullReferenceException: Object reference not set to an instance of an object.
   at AirMonitor.ViewModels.ProtocolParseConfigViewModel.SavePhysicalQuantityAsync() in D:\00 AirMonitor\AirMonitor\ViewModels\ProtocolParseConfigViewModel.cs:line 741
2025-06-24 15:39:04.891 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:39:04.917 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:39:04.917 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:39:04.918 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:39:04.918 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:39:04.944 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:39:04.945 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:39:04.949 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:39:05.739 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:39:05.741 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:39:07.622 +08:00 [INF] [1] : 显示帧数据监听窗体
2025-06-24 15:39:07.766 +08:00 [INF] [1] : 帧数据监听已启动
2025-06-24 15:39:07.771 +08:00 [INF] [1] : 帧数据监听窗体已打开
2025-06-24 15:39:08.832 +08:00 [INF] [1] : 帧数据监听已停止
2025-06-24 15:39:10.011 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:39:10.016 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:39:10.147 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 5 个协议解析配置
2025-06-24 15:39:10.151 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 16 个物理量定义
2025-06-24 15:39:10.246 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:39:10.373 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:39:17.884 +08:00 [INF] [1] : 开始创建新的物理量定义
2025-06-24 15:39:19.918 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: 新物理量
2025-06-24 15:39:22.580 +08:00 [ERR] [1] : ViewModel操作出错: 保存物理量
System.NullReferenceException: Object reference not set to an instance of an object.
   at AirMonitor.ViewModels.ProtocolParseConfigViewModel.SavePhysicalQuantityAsync() in D:\00 AirMonitor\AirMonitor\ViewModels\ProtocolParseConfigViewModel.cs:line 741
2025-06-24 15:39:28.139 +08:00 [INF] [1] : 开始创建新的物理量定义
2025-06-24 15:39:52.724 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: 新物理量
2025-06-24 15:40:01.385 +08:00 [ERR] [1] : ViewModel操作出错: 保存物理量
System.NullReferenceException: Object reference not set to an instance of an object.
   at AirMonitor.ViewModels.ProtocolParseConfigViewModel.SavePhysicalQuantityAsync() in D:\00 AirMonitor\AirMonitor\ViewModels\ProtocolParseConfigViewModel.cs:line 741
2025-06-24 15:40:10.755 +08:00 [INF] [1] : 开始编辑物理量定义: IndoorTempFixFlag
2025-06-24 15:40:15.702 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: IndoorTempFixFlag
2025-06-24 15:41:18.486 +08:00 [ERR] [1] : ViewModel操作出错: 保存物理量
System.NullReferenceException: Object reference not set to an instance of an object.
   at AirMonitor.ViewModels.ProtocolParseConfigViewModel.SavePhysicalQuantityAsync() in D:\00 AirMonitor\AirMonitor\ViewModels\ProtocolParseConfigViewModel.cs:line 741
2025-06-24 15:45:30.537 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:45:30.566 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:45:30.566 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:45:30.567 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:45:30.568 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:45:30.579 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:45:30.579 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:45:30.584 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:45:31.518 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:45:31.520 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:45:33.449 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:45:33.453 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:45:33.605 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 5 个协议解析配置
2025-06-24 15:45:33.610 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 27 个物理量定义
2025-06-24 15:45:33.713 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:45:33.889 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:45:47.851 +08:00 [INF] [1] : 开始编辑物理量定义: IndoorTempFixFlag
2025-06-24 15:45:52.121 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: IndoorTempFixFlag
2025-06-24 15:45:52.132 +08:00 [INF] [1] : 物理量定义保存成功: IndoorTempFixFlag
2025-06-24 15:45:57.923 +08:00 [INF] [1] : 开始编辑物理量定义: IndoorTempFixFlag
2025-06-24 15:46:04.262 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: IndoorTempFixFlag
2025-06-24 15:46:04.269 +08:00 [INF] [1] : 物理量定义保存成功: IndoorTempFixFlag
2025-06-24 15:46:08.329 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:46:08.331 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:46:09.979 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:46:09.981 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:46:11.090 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:46:11.092 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:46:12.268 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:46:12.270 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:46:13.312 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:46:13.314 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:46:14.583 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:46:14.585 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:50:44.059 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:50:44.083 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:50:44.084 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:50:44.085 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:50:44.085 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:50:44.102 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:50:44.102 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:50:44.106 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:50:44.946 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:50:44.949 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:50:46.233 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:50:46.238 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:50:46.510 +08:00 [INF] [15] AirMonitor.Services.ProtocolParseConfigService: 加载了 3 个协议解析配置
2025-06-24 15:50:46.515 +08:00 [INF] [15] AirMonitor.Services.ProtocolParseConfigService: 加载了 31 个物理量定义
2025-06-24 15:50:46.609 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:50:46.775 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:51:10.854 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 商用内外机通讯协议示例配置 (a3837eb8-42f4-4f7b-a8cb-76823ebb8f3d)
2025-06-24 15:51:10.860 +08:00 [INF] [1] : 协议解析配置删除成功: 商用内外机通讯协议示例配置
2025-06-24 15:51:12.701 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置删除成功: 模块机通讯协议示例配置 (7e785402-47bd-4247-8d23-2903bff393f2)
2025-06-24 15:51:12.704 +08:00 [INF] [1] : 协议解析配置删除成功: 模块机通讯协议示例配置
2025-06-24 15:51:16.712 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:51:17.289 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:51:17.483 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:51:17.673 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:51:23.123 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:51:23.146 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:51:23.147 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:51:23.147 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:51:23.147 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:51:23.157 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:51:23.157 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:51:23.162 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:51:24.026 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:51:24.028 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:51:25.751 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:51:25.756 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:51:25.896 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 15:51:25.903 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 31 个物理量定义
2025-06-24 15:51:26.008 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:51:26.160 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:51:31.042 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:51:31.047 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:51:32.166 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:51:32.169 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:51:33.148 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:51:33.150 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:51:34.116 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:51:34.118 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:51:35.012 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:51:35.014 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:51:35.835 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:51:35.836 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:51:36.619 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:51:36.621 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:51:38.715 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:51:38.718 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:51:39.626 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: 新物理量
2025-06-24 15:51:39.628 +08:00 [INF] [1] : 物理量定义删除成功: 新物理量
2025-06-24 15:51:41.630 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:51:41.631 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:51:42.470 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:51:42.472 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:51:43.271 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:51:43.273 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:51:44.169 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:51:44.173 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:51:45.017 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:51:45.019 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:51:45.902 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:51:45.905 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:51:46.748 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:51:46.751 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:51:48.111 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: IndoorTempFixFlag
2025-06-24 15:51:48.113 +08:00 [INF] [1] : 物理量定义删除成功: 支持内机过冷/热度修正的新版本外机程序标志
2025-06-24 15:51:49.603 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:51:49.605 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:51:55.116 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:51:55.139 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:51:55.139 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:51:55.140 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:51:55.140 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:51:55.152 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:51:55.152 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:51:55.160 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:51:55.884 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:51:55.886 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:51:57.211 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:51:57.218 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:51:57.348 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 15:51:57.356 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 25 个物理量定义
2025-06-24 15:51:57.448 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:51:57.594 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:52:03.610 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:52:03.615 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:52:05.226 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:52:05.228 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:52:06.379 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:52:06.381 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:52:07.371 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:52:07.375 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:52:08.371 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:52:08.374 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:52:09.330 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:52:09.332 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:52:10.095 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:52:10.097 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:52:11.111 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: 新物理量
2025-06-24 15:52:11.113 +08:00 [INF] [1] : 物理量定义删除成功: 新物理量
2025-06-24 15:52:11.869 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:52:11.871 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:52:12.584 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:52:12.586 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:52:13.326 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:52:13.329 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:52:14.086 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:52:14.088 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:52:14.808 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:52:14.810 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:52:15.886 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: IndoorTempFixFlag
2025-06-24 15:52:15.887 +08:00 [INF] [1] : 物理量定义删除成功: 支持内机过冷/热度修正的新版本外机程序标志
2025-06-24 15:52:16.710 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:52:16.712 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:52:22.019 +08:00 [INF] [1] : 开始编辑协议解析配置: A1帧配置
2025-06-24 15:52:23.664 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: A1帧配置 (96bc5af9-9fb3-4bc5-9068-1052eeef82e1)
2025-06-24 15:52:23.664 +08:00 [INF] [1] : 协议解析配置保存成功: A1帧配置
2025-06-24 15:52:29.569 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:52:29.649 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:52:29.685 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:52:34.014 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:52:34.017 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:52:34.946 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:52:34.949 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:52:35.821 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:52:35.823 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:52:36.655 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:52:36.658 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:52:39.324 +08:00 [INF] [1] : 开始编辑协议解析配置: A1帧配置
2025-06-24 15:52:40.824 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:52:40.827 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:52:41.552 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:52:41.554 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:52:42.312 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:52:42.313 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:52:42.989 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:52:42.990 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:52:44.244 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:52:44.245 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:52:45.074 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:52:45.076 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:52:45.941 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 配置保存成功: A1帧配置 (96bc5af9-9fb3-4bc5-9068-1052eeef82e1)
2025-06-24 15:52:45.942 +08:00 [INF] [1] : 协议解析配置保存成功: A1帧配置
2025-06-24 15:52:49.295 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 17 个物理量定义
2025-06-24 15:52:49.297 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:52:49.482 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:52:49.671 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:52:52.203 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:52:52.203 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:55:53.761 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:55:53.783 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:55:53.784 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:55:53.784 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:55:53.785 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:55:53.792 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:55:53.792 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:55:53.796 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:55:54.534 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:55:54.536 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:55:56.104 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:55:56.109 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:55:56.265 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 15:55:56.273 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 17 个物理量定义
2025-06-24 15:55:56.384 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:55:56.542 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:56:00.471 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: default-quantities.json
2025-06-24 15:56:00.472 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:56:00.495 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:56:01.604 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:56:01.610 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:56:02.471 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:56:02.477 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:56:03.509 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:56:03.516 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:56:04.482 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:56:04.509 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:56:05.546 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 默认分组.json
2025-06-24 15:56:05.548 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: 新物理量
2025-06-24 15:56:05.553 +08:00 [INF] [1] : 物理量定义删除成功: 新物理量
2025-06-24 15:56:06.427 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:56:06.433 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:56:07.220 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:56:07.224 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:56:08.086 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:56:08.090 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:56:09.416 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:56:09.421 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:56:10.032 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:56:10.035 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:56:10.746 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 环境参数.json
2025-06-24 15:56:10.746 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:56:10.750 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:56:11.471 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 设备状态.json
2025-06-24 15:56:11.471 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:56:11.474 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:56:12.632 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: DATA0.json
2025-06-24 15:56:12.632 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: IndoorTempFixFlag
2025-06-24 15:56:12.636 +08:00 [INF] [1] : 物理量定义删除成功: 支持内机过冷/热度修正的新版本外机程序标志
2025-06-24 15:56:13.408 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 故障信息.json
2025-06-24 15:56:13.408 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:56:13.410 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:56:14.101 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 电气参数.json
2025-06-24 15:56:14.101 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:56:14.103 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:56:15.861 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 物理参数.json
2025-06-24 15:56:15.861 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:56:15.863 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:15.863 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:15.864 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:56:18.464 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:18.465 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:18.465 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:56:18.998 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:18.998 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:18.998 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:56:19.149 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:19.150 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:19.150 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:56:21.375 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:56:21.451 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:21.451 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:21.452 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:56:21.456 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:56:25.447 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:56:25.469 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:56:25.470 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:56:25.471 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:56:25.471 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:56:25.479 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:56:25.479 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:56:25.484 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:56:26.328 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:56:26.330 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:56:27.800 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:56:27.804 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:56:27.950 +08:00 [INF] [9] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 15:56:27.979 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:56:28.077 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:56:28.175 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:56:33.087 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: default-quantities.json
2025-06-24 15:56:33.088 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Pressure
2025-06-24 15:56:33.094 +08:00 [INF] [1] : 物理量定义删除成功: 压力
2025-06-24 15:56:34.042 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Humidity
2025-06-24 15:56:34.048 +08:00 [INF] [1] : 物理量定义删除成功: 湿度
2025-06-24 15:56:34.880 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: AlarmStatus
2025-06-24 15:56:34.885 +08:00 [INF] [1] : 物理量定义删除成功: 报警状态
2025-06-24 15:56:35.638 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 环境参数.json
2025-06-24 15:56:35.639 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Temperature
2025-06-24 15:56:35.641 +08:00 [INF] [1] : 物理量定义删除成功: 温度
2025-06-24 15:56:36.402 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Current
2025-06-24 15:56:36.406 +08:00 [INF] [1] : 物理量定义删除成功: 电流
2025-06-24 15:56:37.131 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 故障信息.json
2025-06-24 15:56:37.131 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: ErrorCode
2025-06-24 15:56:37.133 +08:00 [INF] [1] : 物理量定义删除成功: 故障代码
2025-06-24 15:56:37.933 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: OnOffStatus
2025-06-24 15:56:37.935 +08:00 [INF] [1] : 物理量定义删除成功: 开关状态
2025-06-24 15:56:38.974 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Power
2025-06-24 15:56:38.976 +08:00 [INF] [1] : 物理量定义删除成功: 功率
2025-06-24 15:56:39.981 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 电气参数.json
2025-06-24 15:56:39.981 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: Voltage
2025-06-24 15:56:39.983 +08:00 [INF] [1] : 物理量定义删除成功: 电压
2025-06-24 15:56:40.791 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 删除空的物理量分组文件: 设备状态.json
2025-06-24 15:56:40.791 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量删除成功: RunningStatus
2025-06-24 15:56:40.795 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:40.795 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:40.796 +08:00 [INF] [1] : 物理量定义删除成功: 运行状态
2025-06-24 15:56:44.212 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:44.213 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:44.213 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:56:44.956 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:44.956 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:44.956 +08:00 [INF] [1] : 协议数据解析配置数据已刷新
2025-06-24 15:56:48.438 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:56:48.507 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:48.507 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 15:56:48.508 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:56:48.513 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:56:51.828 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:56:51.828 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:56:55.205 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:56:55.230 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:56:55.230 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:56:55.231 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:56:55.231 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:56:55.240 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:56:55.240 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:56:55.244 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:56:55.935 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:56:55.936 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 15:56:57.344 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 15:56:57.348 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 15:56:57.492 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 15:56:57.500 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 10 个物理量定义
2025-06-24 15:56:57.593 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 15:56:57.671 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 15:57:01.404 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已停止
2025-06-24 15:57:01.404 +08:00 [INF] [1] : === AirMonitor 正常关闭 ===
2025-06-24 15:59:58.136 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 15:59:58.157 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 15:59:58.157 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 15:59:58.158 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 15:59:58.158 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 15:59:58.167 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 15:59:58.167 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 15:59:58.171 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 15:59:58.822 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 15:59:58.824 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 16:00:00.441 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 16:00:00.445 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 16:00:00.661 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 16:00:00.662 +08:00 [INF] [5] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 16:00:00.765 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 16:00:00.766 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 加载了 0 个物理量定义
2025-06-24 16:00:00.767 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 16:00:00.790 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 16:00:05.034 +08:00 [INF] [1] : 开始创建新的物理量定义
2025-06-24 16:01:26.862 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 物理量保存成功: InTempFixEn
2025-06-24 16:01:26.867 +08:00 [INF] [1] : 物理量定义保存成功: InTempFixEn
2025-06-24 16:01:35.720 +08:00 [INF] [1] : 开始创建新的数据映射配置
2025-06-24 16:06:36.652 +08:00 [INF] [1] : 日志系统初始化完成，日志级别: "Information"，日志文件: Logs/app-.log
2025-06-24 16:06:36.677 +08:00 [INF] [1] : === AirMonitor v1.0.0 启动 ===
2025-06-24 16:06:36.678 +08:00 [INF] [1] : 操作系统: Microsoft Windows NT 10.0.19045.0
2025-06-24 16:06:36.679 +08:00 [INF] [1] : 运行时版本: 9.0.5
2025-06-24 16:06:36.679 +08:00 [INF] [1] : 工作目录: D:\00 AirMonitor\AirMonitor\bin\Debug\net9.0-windows
2025-06-24 16:06:36.696 +08:00 [INF] [1] : 用户名: wangzhuhui
2025-06-24 16:06:36.696 +08:00 [INF] [1] : 机器名: JD-ITA028088-PC
2025-06-24 16:06:36.701 +08:00 [INF] [1] AirMonitor.Services.DataBridgeService: 数据桥接服务已启动
2025-06-24 16:06:37.687 +08:00 [INF] [1] : 正在初始化主窗口...
2025-06-24 16:06:37.689 +08:00 [INF] [1] : 主窗口初始化完成
2025-06-24 16:06:39.317 +08:00 [INF] [1] : 显示协议数据解析配置窗口
2025-06-24 16:06:39.321 +08:00 [INF] [1] AirMonitor.Services.ProtocolParseConfigService: 协议数据解析配置服务已初始化
2025-06-24 16:06:39.475 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个协议解析配置
2025-06-24 16:06:39.480 +08:00 [INF] [10] AirMonitor.Services.ProtocolParseConfigService: 加载了 1 个物理量定义
2025-06-24 16:06:39.581 +08:00 [INF] [1] : 协议数据解析配置界面初始化完成
2025-06-24 16:06:39.609 +08:00 [INF] [1] : 协议数据解析配置窗口已打开
2025-06-24 16:06:46.082 +08:00 [INF] [1] : 开始创建新的数据映射配置
2025-06-24 16:06:56.943 +08:00 [INF] [1] : 开始编辑数据映射配置: 新数据映射
2025-06-24 16:07:38.911 +08:00 [INF] [1] : 开始编辑数据映射配置: 新数据映射
2025-06-24 16:07:39.087 +08:00 [INF] [1] : 开始编辑数据映射配置: 新数据映射
