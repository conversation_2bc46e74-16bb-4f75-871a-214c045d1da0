<Window
    x:Class="AirMonitor.Views.ProtocolParseConfigWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:AirMonitor.Controls"
    xmlns:converters="clr-namespace:AirMonitor.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding Title, FallbackValue='协议数据解析配置'}"
    Width="1200"
    Height="800"
    MinWidth="1000"
    MinHeight="600"
    vm:ViewModelLocator.AutoWireViewModel="True"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <!--  转换器定义  -->
        <converters:NullToBooleanConverter x:Key="NullToBooleanConverter" />

        <!--  样式定义  -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,0,0,10" />
        </Style>

        <Style x:Key="SectionBorderStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="Padding" Value="10" />
            <Setter Property="Margin" Value="5" />
            <Setter Property="Background" Value="White" />
        </Style>

        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2" />
            <Setter Property="Padding" Value="8,4" />
            <Setter Property="MinWidth" Value="80" />
        </Style>

        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="CanUserAddRows" Value="False" />
            <Setter Property="CanUserDeleteRows" Value="False" />
            <Setter Property="SelectionMode" Value="Single" />
            <Setter Property="GridLinesVisibility" Value="Horizontal" />
            <Setter Property="HeadersVisibility" Value="Column" />
            <Setter Property="AlternatingRowBackground" Value="#F8F8F8" />
        </Style>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,5,0,5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  工具栏  -->
        <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}">
            <StackPanel Orientation="Horizontal">
                <Button
                    Command="{Binding NewConfigCommand}"
                    Content="新建配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding EditConfigCommand}"
                    Content="编辑配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding DeleteConfigCommand}"
                    Content="删除配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding CopyConfigCommand}"
                    Content="复制配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Separator Margin="10,0" />
                <Button
                    Command="{Binding ImportConfigCommand}"
                    Content="导入配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding ExportConfigCommand}"
                    Content="导出配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding BatchImportConfigCommand}"
                    Content="批量导入"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding ExportAllConfigsCommand}"
                    Content="导出全部"
                    Style="{StaticResource ToolbarButtonStyle}" />

                <Separator Margin="10,0" />
                <Button
                    Command="{Binding ValidateConfigCommand}"
                    Content="验证配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding TestConfigCommand}"
                    Content="测试配置"
                    Style="{StaticResource ToolbarButtonStyle}" />
                <Button
                    Command="{Binding RefreshCommand}"
                    Content="刷新"
                    Style="{StaticResource ToolbarButtonStyle}" />

                <!--  搜索框  -->
                <StackPanel Margin="20,0,0,0" Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        VerticalAlignment="Center"
                        Text="搜索:" />
                    <TextBox
                        Width="200"
                        VerticalAlignment="Center"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />
                    <Button
                        Command="{Binding SearchCommand}"
                        Content="搜索"
                        Style="{StaticResource ToolbarButtonStyle}" />
                </StackPanel>

                <!--  协议类型筛选  -->
                <StackPanel Margin="20,0,0,0" Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,5,0"
                        VerticalAlignment="Center"
                        Text="协议类型:" />
                    <ComboBox
                        Width="150"
                        VerticalAlignment="Center"
                        ItemsSource="{Binding ProtocolTypes}"
                        SelectedItem="{Binding FilterProtocolType}" />
                </StackPanel>
            </StackPanel>
        </Border>

        <!--  主内容区域  -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧配置列表  -->
            <Border Grid.Column="0" Style="{StaticResource SectionBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Grid.Row="0"
                        Style="{StaticResource HeaderTextStyle}"
                        Text="协议解析配置" />

                    <DataGrid
                        Grid.Row="1"
                        ItemsSource="{Binding Configs}"
                        SelectedItem="{Binding SelectedConfig}"
                        Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Name}"
                                Header="配置名称" />
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding ProtocolType}"
                                Header="协议类型" />
                            <DataGridCheckBoxColumn
                                Width="50"
                                Binding="{Binding IsEnabled}"
                                Header="启用" />
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <GridSplitter
                Grid.Column="1"
                Width="5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Background="#E0E0E0" />

            <!--  右侧详细配置  -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  配置基本信息  -->
                <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock
                            Grid.Row="0"
                            Style="{StaticResource HeaderTextStyle}"
                            Text="配置信息" />

                        <Grid Grid.Row="1" IsEnabled="{Binding IsConfigEditing}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="配置名称:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding SelectedConfig.Name, UpdateSourceTrigger=PropertyChanged}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="协议类型:" />
                            <ComboBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                ItemsSource="{Binding ProtocolTypes}"
                                SelectedItem="{Binding SelectedConfig.ProtocolType}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="命令码:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding SelectedConfig.CommandCode, UpdateSourceTrigger=PropertyChanged}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="版本:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding SelectedConfig.Version, UpdateSourceTrigger=PropertyChanged}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Top"
                                Text="描述:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Grid.ColumnSpan="3"
                                Height="60"
                                Margin="0,0,0,5"
                                AcceptsReturn="True"
                                Text="{Binding SelectedConfig.Description, UpdateSourceTrigger=PropertyChanged}"
                                TextWrapping="Wrap" />

                            <StackPanel
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">
                                <Button
                                    Command="{Binding SaveConfigCommand}"
                                    Content="保存"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding CancelEditCommand}"
                                    Content="取消"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  详细配置选项卡  -->
                <TabControl Grid.Row="1" Margin="5,0,0,0">
                    <TabItem Header="物理量定义">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <!--  物理量工具栏  -->
                            <StackPanel
                                Grid.Row="0"
                                Margin="5"
                                Orientation="Horizontal">
                                <Button
                                    Command="{Binding NewPhysicalQuantityCommand}"
                                    Content="新建物理量"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding EditPhysicalQuantityCommand}"
                                    Content="编辑物理量"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding DeletePhysicalQuantityCommand}"
                                    Content="删除物理量"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding SavePhysicalQuantityCommand}"
                                    Content="保存物理量"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                            </StackPanel>

                            <!--  物理量列表和编辑区域  -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="5" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!--  物理量列表  -->
                                <DataGrid
                                    Grid.Column="0"
                                    ItemsSource="{Binding PhysicalQuantities}"
                                    SelectedItem="{Binding SelectedPhysicalQuantity}"
                                    Style="{StaticResource DataGridStyle}">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn
                                            Width="100"
                                            Binding="{Binding Name}"
                                            Header="名称" />
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding DisplayName}"
                                            Header="显示名称" />
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding DataType}"
                                            Header="数据类型" />
                                        <DataGridTextColumn
                                            Width="60"
                                            Binding="{Binding Unit}"
                                            Header="单位" />
                                        <DataGridCheckBoxColumn
                                            Width="50"
                                            Binding="{Binding IsEnabled}"
                                            Header="启用" />
                                    </DataGrid.Columns>
                                </DataGrid>

                                <GridSplitter
                                    Grid.Column="1"
                                    Width="5"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Background="#E0E0E0" />

                                <!--  物理量编辑区域  -->
                                <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto">
                                    <Border Style="{StaticResource SectionBorderStyle}">
                                        <Grid IsEnabled="{Binding IsPhysicalQuantityEditing}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="物理量名称:" />
                                            <TextBox
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.Name, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="显示名称:" />
                                            <TextBox
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.DisplayName, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="数据类型:" />
                                            <ComboBox
                                                Grid.Row="2"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                ItemsSource="{Binding DataTypes}"
                                                SelectedItem="{Binding SelectedPhysicalQuantity.DataType}" />

                                            <TextBlock
                                                Grid.Row="3"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="单位:" />
                                            <TextBox
                                                Grid.Row="3"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.Unit, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="4"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="缩放因子:" />
                                            <TextBox
                                                Grid.Row="4"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.ScaleFactor, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="5"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="偏移量:" />
                                            <TextBox
                                                Grid.Row="5"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.Offset, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="6"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="最小值:" />
                                            <TextBox
                                                Grid.Row="6"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.MinValue, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="7"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="最大值:" />
                                            <TextBox
                                                Grid.Row="7"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.MaxValue, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="8"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="小数位数:" />
                                            <TextBox
                                                Grid.Row="8"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.DecimalPlaces, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="9"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="分组名称:" />
                                            <TextBox
                                                Grid.Row="9"
                                                Grid.Column="1"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedPhysicalQuantity.GroupName, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="10"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Top"
                                                Text="描述:" />
                                            <TextBox
                                                Grid.Row="10"
                                                Grid.Column="1"
                                                Height="60"
                                                Margin="0,0,0,5"
                                                AcceptsReturn="True"
                                                Text="{Binding SelectedPhysicalQuantity.Description, UpdateSourceTrigger=PropertyChanged}"
                                                TextWrapping="Wrap" />

                                            <CheckBox
                                                Grid.Row="11"
                                                Grid.Column="1"
                                                Margin="0,5,0,0"
                                                Content="启用"
                                                IsChecked="{Binding SelectedPhysicalQuantity.IsEnabled}" />
                                        </Grid>
                                    </Border>
                                </ScrollViewer>
                            </Grid>
                        </Grid>
                    </TabItem>

                    <TabItem Header="数据映射配置">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <!--  数据映射工具栏  -->
                            <StackPanel
                                Grid.Row="0"
                                Margin="5"
                                Orientation="Horizontal">
                                <Button
                                    Command="{Binding NewDataMappingCommand}"
                                    Content="新建映射"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding EditDataMappingCommand}"
                                    Content="编辑映射"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding DeleteDataMappingCommand}"
                                    Content="删除映射"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Button
                                    Command="{Binding SaveDataMappingCommand}"
                                    Content="保存映射"
                                    Style="{StaticResource ToolbarButtonStyle}" />
                                <Separator Margin="10,0" />
                                <ComboBox
                                    Width="120"
                                    VerticalAlignment="Center"
                                    IsEnabled="{Binding IsDataMappingEditing}"
                                    ItemsSource="{Binding MappingTypes}"
                                    SelectedItem="{Binding SelectedDataMapping.MappingType}" />
                            </StackPanel>

                            <!--  数据映射列表和编辑区域  -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="5" />
                                    <ColumnDefinition Width="2*" />
                                </Grid.ColumnDefinitions>

                                <!--  数据映射列表  -->
                                <DataGrid
                                    Grid.Column="0"
                                    ItemsSource="{Binding DataMappings}"
                                    SelectedItem="{Binding SelectedDataMapping}"
                                    Style="{StaticResource DataGridStyle}">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn
                                            Width="120"
                                            Binding="{Binding Name}"
                                            Header="映射名称" />
                                        <DataGridTextColumn
                                            Width="100"
                                            Binding="{Binding MappingType}"
                                            Header="映射类型" />
                                        <DataGridTextColumn
                                            Width="100"
                                            Binding="{Binding PhysicalQuantityName}"
                                            Header="物理量" />
                                        <DataGridCheckBoxColumn
                                            Width="50"
                                            Binding="{Binding IsEnabled}"
                                            Header="启用" />
                                    </DataGrid.Columns>
                                </DataGrid>

                                <GridSplitter
                                    Grid.Column="1"
                                    Width="5"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Background="#E0E0E0" />

                                <!--  数据映射编辑区域  -->
                                <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto">
                                    <Border Style="{StaticResource SectionBorderStyle}">
                                        <Grid IsEnabled="{Binding IsDataMappingEditing}">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                            </Grid.RowDefinitions>

                                            <!--  基本信息  -->
                                            <Grid Grid.Row="0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>

                                                <TextBlock
                                                    Grid.Row="0"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="映射名称:" />
                                                <TextBox
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    Margin="0,0,0,5"
                                                    Text="{Binding SelectedDataMapping.Name, UpdateSourceTrigger=PropertyChanged}" />

                                                <TextBlock
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="映射类型:" />
                                                <ComboBox
                                                    Grid.Row="1"
                                                    Grid.Column="1"
                                                    Margin="0,0,0,5"
                                                    ItemsSource="{Binding MappingTypes}"
                                                    SelectedItem="{Binding SelectedDataMapping.MappingType}" />

                                                <TextBlock
                                                    Grid.Row="2"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="目标物理量:" />
                                                <ComboBox
                                                    Grid.Row="2"
                                                    Grid.Column="1"
                                                    Margin="0,0,0,5"
                                                    DisplayMemberPath="DisplayName"
                                                    ItemsSource="{Binding PhysicalQuantities}"
                                                    SelectedValue="{Binding SelectedDataMapping.PhysicalQuantityName}"
                                                    SelectedValuePath="Name" />

                                                <TextBlock
                                                    Grid.Row="3"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Top"
                                                    Text="描述:" />
                                                <TextBox
                                                    Grid.Row="3"
                                                    Grid.Column="1"
                                                    Height="60"
                                                    Margin="0,0,0,5"
                                                    AcceptsReturn="True"
                                                    Text="{Binding SelectedDataMapping.Description, UpdateSourceTrigger=PropertyChanged}"
                                                    TextWrapping="Wrap" />

                                                <CheckBox
                                                    Grid.Row="4"
                                                    Grid.Column="1"
                                                    Margin="0,5,0,10"
                                                    Content="启用此映射"
                                                    IsChecked="{Binding SelectedDataMapping.IsEnabled}" />
                                            </Grid>

                                            <Separator Grid.Row="1" Margin="0,5" />

                                            <!--  映射配置详情  -->
                                            <ContentControl Grid.Row="2" Content="{Binding SelectedDataMapping}">
                                                <ContentControl.Resources>
                                                    <!--  字节级映射模板  -->
                                                    <DataTemplate x:Key="ByteMappingTemplate">
                                                        <StackPanel>
                                                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="字节范围配置" />
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="*" />
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="*" />
                                                                </Grid.ColumnDefinitions>
                                                                <Grid.RowDefinitions>
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                </Grid.RowDefinitions>

                                                                <TextBlock
                                                                    Grid.Row="0"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="起始索引:" />
                                                                <TextBox
                                                                    Grid.Row="0"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,10,5"
                                                                    Text="{Binding ByteRange.StartIndex, UpdateSourceTrigger=PropertyChanged}" />

                                                                <TextBlock
                                                                    Grid.Row="0"
                                                                    Grid.Column="2"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="字节长度:" />
                                                                <TextBox
                                                                    Grid.Row="0"
                                                                    Grid.Column="3"
                                                                    Margin="0,0,0,5"
                                                                    Text="{Binding ByteRange.Length, UpdateSourceTrigger=PropertyChanged}" />

                                                                <TextBlock
                                                                    Grid.Row="1"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="字节序:" />
                                                                <ComboBox
                                                                    Grid.Row="1"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,10,5"
                                                                    ItemsSource="{Binding DataContext.ByteOrders, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                    SelectedItem="{Binding ByteRange.ByteOrder}" />
                                                            </Grid>
                                                        </StackPanel>
                                                    </DataTemplate>

                                                    <!--  位级映射模板  -->
                                                    <DataTemplate x:Key="BitMappingTemplate">
                                                        <StackPanel>
                                                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="位位置配置" />
                                                            <controls:BytePositionSelector
                                                                BitPosition="{Binding BitPositions[0], UpdateSourceTrigger=PropertyChanged}"
                                                                Margin="0,10" />
                                                        </StackPanel>
                                                    </DataTemplate>

                                                    <!--  跨字节位拼接模板  -->
                                                    <DataTemplate x:Key="CrossByteBitMappingTemplate">
                                                        <StackPanel>
                                                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="跨字节位拼接配置" />
                                                            <TextBlock
                                                                Margin="0,0,0,10"
                                                                FontStyle="Italic"
                                                                Text="配置多个位位置，系统将按目标位偏移进行拼接" />

                                                            <DataGrid
                                                                Height="200"
                                                                AutoGenerateColumns="False"
                                                                CanUserAddRows="True"
                                                                CanUserDeleteRows="True"
                                                                ItemsSource="{Binding BitPositions}">
                                                                <DataGrid.Columns>
                                                                    <DataGridTextColumn
                                                                        Width="80"
                                                                        Binding="{Binding ByteIndex}"
                                                                        Header="字节索引" />
                                                                    <DataGridTextColumn
                                                                        Width="60"
                                                                        Binding="{Binding BitIndex}"
                                                                        Header="位索引" />
                                                                    <DataGridTextColumn
                                                                        Width="60"
                                                                        Binding="{Binding BitLength}"
                                                                        Header="位长度" />
                                                                    <DataGridTextColumn
                                                                        Width="80"
                                                                        Binding="{Binding TargetBitOffset}"
                                                                        Header="目标位偏移" />
                                                                </DataGrid.Columns>
                                                            </DataGrid>
                                                        </StackPanel>
                                                    </DataTemplate>

                                                    <!--  索引映射模板  -->
                                                    <DataTemplate x:Key="IndexMappingTemplate">
                                                        <StackPanel>
                                                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="索引映射配置" />
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="*" />
                                                                </Grid.ColumnDefinitions>
                                                                <Grid.RowDefinitions>
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                    <RowDefinition Height="Auto" />
                                                                </Grid.RowDefinitions>

                                                                <TextBlock
                                                                    Grid.Row="0"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="索引值:" />
                                                                <TextBox
                                                                    Grid.Row="0"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,0,5"
                                                                    Text="{Binding IndexMapping.IndexValue, UpdateSourceTrigger=PropertyChanged}" />

                                                                <TextBlock
                                                                    Grid.Row="1"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="数据起始索引:" />
                                                                <TextBox
                                                                    Grid.Row="1"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,0,5"
                                                                    Text="{Binding IndexMapping.DataByteRange.StartIndex, UpdateSourceTrigger=PropertyChanged}" />

                                                                <TextBlock
                                                                    Grid.Row="2"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="数据字节长度:" />
                                                                <TextBox
                                                                    Grid.Row="2"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,0,5"
                                                                    Text="{Binding IndexMapping.DataByteRange.Length, UpdateSourceTrigger=PropertyChanged}" />

                                                                <TextBlock
                                                                    Grid.Row="3"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Center"
                                                                    Text="数据字节序:" />
                                                                <ComboBox
                                                                    Grid.Row="3"
                                                                    Grid.Column="1"
                                                                    Margin="0,0,0,5"
                                                                    ItemsSource="{Binding DataContext.ByteOrders, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                    SelectedItem="{Binding IndexMapping.DataByteRange.ByteOrder}" />

                                                                <TextBlock
                                                                    Grid.Row="4"
                                                                    Grid.Column="0"
                                                                    Margin="0,0,10,5"
                                                                    VerticalAlignment="Top"
                                                                    Text="条件表达式:" />
                                                                <TextBox
                                                                    Grid.Row="4"
                                                                    Grid.Column="1"
                                                                    Height="60"
                                                                    Margin="0,0,0,5"
                                                                    Text="{Binding IndexMapping.ConditionExpression, UpdateSourceTrigger=PropertyChanged}"
                                                                    TextWrapping="Wrap" />
                                                            </Grid>
                                                        </StackPanel>
                                                    </DataTemplate>
                                                </ContentControl.Resources>

                                                <ContentControl.Style>
                                                    <Style TargetType="ContentControl">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding MappingType}" Value="ByteMapping">
                                                                <Setter Property="ContentTemplate" Value="{StaticResource ByteMappingTemplate}" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding MappingType}" Value="BitMapping">
                                                                <Setter Property="ContentTemplate" Value="{StaticResource BitMappingTemplate}" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding MappingType}" Value="CrossByteBitMapping">
                                                                <Setter Property="ContentTemplate" Value="{StaticResource CrossByteBitMappingTemplate}" />
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding MappingType}" Value="IndexMapping">
                                                                <Setter Property="ContentTemplate" Value="{StaticResource IndexMappingTemplate}" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ContentControl.Style>
                                            </ContentControl>
                                        </Grid>
                                    </Border>
                                </ScrollViewer>
                            </Grid>
                        </Grid>
                    </TabItem>

                    <TabItem Header="索引配置">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <Border Style="{StaticResource SectionBorderStyle}">
                                <Grid IsEnabled="{Binding IsConfigEditing}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <!--  索引配置开关  -->
                                    <CheckBox
                                        Grid.Row="0"
                                        Margin="0,0,0,15"
                                        Content="启用索引解析配置"
                                        FontWeight="Bold"
                                        IsChecked="{Binding SelectedConfig.IndexParseConfig, Converter={StaticResource NullToBooleanConverter}}" />

                                    <!--  索引字节范围配置  -->
                                    <GroupBox
                                        Grid.Row="1"
                                        Margin="0,0,0,10"
                                        Header="索引字节范围配置">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="索引起始位置:" />
                                            <TextBox
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Margin="0,0,10,5"
                                                Text="{Binding SelectedConfig.IndexParseConfig.IndexByteRange.StartIndex, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="0"
                                                Grid.Column="2"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="索引字节长度:" />
                                            <TextBox
                                                Grid.Row="0"
                                                Grid.Column="3"
                                                Margin="0,0,0,5"
                                                Text="{Binding SelectedConfig.IndexParseConfig.IndexByteRange.Length, UpdateSourceTrigger=PropertyChanged}" />

                                            <TextBlock
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                Margin="0,0,10,5"
                                                VerticalAlignment="Center"
                                                Text="索引字节序:" />
                                            <ComboBox
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Margin="0,0,10,5"
                                                ItemsSource="{Binding ByteOrders}"
                                                SelectedItem="{Binding SelectedConfig.IndexParseConfig.IndexByteRange.ByteOrder}" />
                                        </Grid>
                                    </GroupBox>

                                    <!--  索引数量配置  -->
                                    <GroupBox
                                        Grid.Row="2"
                                        Margin="0,0,0,10"
                                        Header="索引数量配置（可选）">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <CheckBox
                                                Grid.Row="0"
                                                Margin="0,0,0,10"
                                                Content="启用索引数量解析"
                                                IsChecked="{Binding SelectedConfig.IndexParseConfig.IndexCountByteRange, Converter={StaticResource NullToBooleanConverter}}" />

                                            <Grid Grid.Row="1">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>

                                                <TextBlock
                                                    Grid.Row="0"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="数量起始位置:" />
                                                <TextBox
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    Margin="0,0,10,5"
                                                    Text="{Binding SelectedConfig.IndexParseConfig.IndexCountByteRange.StartIndex, UpdateSourceTrigger=PropertyChanged}" />

                                                <TextBlock
                                                    Grid.Row="0"
                                                    Grid.Column="2"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="数量字节长度:" />
                                                <TextBox
                                                    Grid.Row="0"
                                                    Grid.Column="3"
                                                    Margin="0,0,0,5"
                                                    Text="{Binding SelectedConfig.IndexParseConfig.IndexCountByteRange.Length, UpdateSourceTrigger=PropertyChanged}" />

                                                <TextBlock
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="数量字节序:" />
                                                <ComboBox
                                                    Grid.Row="1"
                                                    Grid.Column="1"
                                                    Margin="0,0,10,5"
                                                    ItemsSource="{Binding ByteOrders}"
                                                    SelectedItem="{Binding SelectedConfig.IndexParseConfig.IndexCountByteRange.ByteOrder}" />
                                            </Grid>
                                        </Grid>
                                    </GroupBox>

                                    <!--  索引值解析配置  -->
                                    <GroupBox
                                        Grid.Row="3"
                                        Margin="0,0,0,10"
                                        Header="索引值解析配置">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <CheckBox
                                                Grid.Row="0"
                                                Margin="0,0,0,10"
                                                Content="将索引值本身解析为物理量"
                                                IsChecked="{Binding SelectedConfig.IndexParseConfig.ParseIndexAsPhysicalQuantity}" />

                                            <Grid Grid.Row="1">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>

                                                <TextBlock
                                                    Grid.Row="0"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="索引高字节物理量:" />
                                                <ComboBox
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    Margin="0,0,0,5"
                                                    DisplayMemberPath="DisplayName"
                                                    ItemsSource="{Binding PhysicalQuantities}"
                                                    SelectedValue="{Binding SelectedConfig.IndexParseConfig.IndexHighBytePhysicalQuantityName}"
                                                    SelectedValuePath="Name" />

                                                <TextBlock
                                                    Grid.Row="1"
                                                    Grid.Column="0"
                                                    Margin="0,0,10,5"
                                                    VerticalAlignment="Center"
                                                    Text="索引低字节物理量:" />
                                                <ComboBox
                                                    Grid.Row="1"
                                                    Grid.Column="1"
                                                    Margin="0,0,0,5"
                                                    DisplayMemberPath="DisplayName"
                                                    ItemsSource="{Binding PhysicalQuantities}"
                                                    SelectedValue="{Binding SelectedConfig.IndexParseConfig.IndexLowBytePhysicalQuantityName}"
                                                    SelectedValuePath="Name" />
                                            </Grid>

                                            <TextBlock
                                                Grid.Row="2"
                                                Margin="0,10,0,5"
                                                Style="{StaticResource SectionHeaderStyle}"
                                                Text="索引位解析配置:" />

                                            <DataGrid
                                                Grid.Row="3"
                                                Height="150"
                                                AutoGenerateColumns="False"
                                                CanUserAddRows="True"
                                                CanUserDeleteRows="True"
                                                ItemsSource="{Binding SelectedConfig.IndexParseConfig.IndexBitParseConfigs}">
                                                <DataGrid.Columns>
                                                    <DataGridTextColumn
                                                        Width="80"
                                                        Binding="{Binding BitPosition.ByteIndex}"
                                                        Header="字节索引" />
                                                    <DataGridTextColumn
                                                        Width="60"
                                                        Binding="{Binding BitPosition.BitIndex}"
                                                        Header="位索引" />
                                                    <DataGridTextColumn
                                                        Width="60"
                                                        Binding="{Binding BitPosition.BitLength}"
                                                        Header="位长度" />
                                                    <DataGridComboBoxColumn
                                                        Width="120"
                                                        DisplayMemberPath="DisplayName"
                                                        Header="物理量"
                                                        SelectedValueBinding="{Binding PhysicalQuantityName}"
                                                        SelectedValuePath="Name">
                                                        <DataGridComboBoxColumn.ElementStyle>
                                                            <Style TargetType="ComboBox">
                                                                <Setter Property="ItemsSource" Value="{Binding DataContext.PhysicalQuantities, RelativeSource={RelativeSource AncestorType=Window}}" />
                                                            </Style>
                                                        </DataGridComboBoxColumn.ElementStyle>
                                                        <DataGridComboBoxColumn.EditingElementStyle>
                                                            <Style TargetType="ComboBox">
                                                                <Setter Property="ItemsSource" Value="{Binding DataContext.PhysicalQuantities, RelativeSource={RelativeSource AncestorType=Window}}" />
                                                            </Style>
                                                        </DataGridComboBoxColumn.EditingElementStyle>
                                                    </DataGridComboBoxColumn>
                                                </DataGrid.Columns>
                                            </DataGrid>
                                        </Grid>
                                    </GroupBox>

                                    <!--  配置说明  -->
                                    <GroupBox
                                        Grid.Row="4"
                                        Margin="0,0,0,10"
                                        Header="配置说明">
                                        <TextBlock
                                            FontStyle="Italic"
                                            Foreground="Gray"
                                            TextWrapping="Wrap">
                                            <Run FontWeight="Bold" Text="索引配置用于解析基于idx值的动态数据帧。配置说明：" />
                                            <LineBreak />
                                            <Run Text="• 索引字节范围：指定帧中索引值的位置和长度" />
                                            <LineBreak />
                                            <Run Text="• 索引数量：可选，指定帧中索引数量的位置（如果协议包含此信息）" />
                                            <LineBreak />
                                            <Run Text="• 索引值解析：可以将索引值本身或其高低字节解析为物理量" />
                                            <LineBreak />
                                            <Run Text="• 索引位解析：可以将索引值中的特定位解析为独立的物理量" />
                                            <LineBreak />
                                            <Run Text="• 数据映射：在数据映射配置中，使用索引映射类型来定义不同索引值对应的数据解析规则" />
                                        </TextBlock>
                                    </GroupBox>
                                </Grid>
                            </Border>
                        </ScrollViewer>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>

        <!--  状态栏  -->
        <Border Grid.Row="2" Style="{StaticResource SectionBorderStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage}" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar
                        Width="100"
                        Height="16"
                        Margin="0,0,10,0"
                        IsIndeterminate="{Binding IsBusy}"
                        Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBlock
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Text="{Binding Configs.Count, StringFormat='配置数量: {0}'}" />
                    <TextBlock VerticalAlignment="Center" Text="{Binding PhysicalQuantities.Count, StringFormat='物理量数量: {0}'}" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
