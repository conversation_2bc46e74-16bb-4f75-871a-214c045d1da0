using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AirMonitor.Models;

/// <summary>
/// 数据映射配置
/// </summary>
public class DataMappingConfig
{
    /// <summary>
    /// 映射配置ID（唯一标识）
    /// </summary>
    [Required]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 映射名称
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "映射名称长度必须在1-100字符之间")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 映射描述
    /// </summary>
    [StringLength(500, ErrorMessage = "描述长度不能超过500字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 映射类型
    /// </summary>
    [Required]
    public DataMappingType MappingType { get; set; } = DataMappingType.ByteMapping;

    /// <summary>
    /// 目标物理量名称
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "物理量名称长度必须在1-100字符之间")]
    public string PhysicalQuantityName { get; set; } = string.Empty;

    /// <summary>
    /// 字节范围配置（用于字节级映射）
    /// </summary>
    public ByteRange? ByteRange { get; set; }

    /// <summary>
    /// 位位置列表（用于位级映射和跨字节位拼接）
    /// </summary>
    public List<BitPosition> BitPositions { get; set; } = new();

    /// <summary>
    /// 索引映射配置（用于基于索引的映射）
    /// </summary>
    public IndexMappingConfig? IndexMapping { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 验证映射配置是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfig()
    {
        var errors = new List<string>();

        switch (MappingType)
        {
            case DataMappingType.ByteMapping:
                if (ByteRange == null)
                    errors.Add("字节级映射必须配置字节范围");
                break;

            case DataMappingType.BitMapping:
                if (BitPositions.Count != 1)
                    errors.Add("位级映射必须配置一个位位置");
                break;

            case DataMappingType.CrossByteBitMapping:
                if (BitPositions.Count < 2)
                    errors.Add("跨字节位拼接必须配置至少两个位位置");
                break;

            case DataMappingType.IndexMapping:
                if (IndexMapping == null)
                    errors.Add("索引映射必须配置索引映射信息");
                break;
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            ErrorMessages = errors
        };
    }

    public override string ToString()
    {
        return $"{Name} -> {PhysicalQuantityName} ({MappingType})";
    }
}

/// <summary>
/// 索引映射配置
/// </summary>
public class IndexMappingConfig
{
    /// <summary>
    /// 索引值
    /// </summary>
    [Required]
    public int IndexValue { get; set; }

    /// <summary>
    /// 数据字节范围（在索引数据帧中的位置）
    /// </summary>
    [Required]
    public ByteRange DataByteRange { get; set; } = new();

    /// <summary>
    /// 条件表达式（可选，用于复杂的索引匹配逻辑）
    /// </summary>
    [StringLength(200, ErrorMessage = "条件表达式长度不能超过200字符")]
    public string? ConditionExpression { get; set; }

    public override string ToString()
    {
        return $"Index[{IndexValue}] -> {DataByteRange}";
    }
}

/// <summary>
/// 协议解析配置
/// </summary>
public class ProtocolParseConfig
{
    /// <summary>
    /// 配置ID
    /// </summary>
    [Required]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 配置名称
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "配置名称长度必须在1-100字符之间")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 配置描述
    /// </summary>
    [StringLength(500, ErrorMessage = "描述长度不能超过500字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 协议类型
    /// </summary>
    [Required]
    public DataPacketProtocolType ProtocolType { get; set; } = DataPacketProtocolType.CommercialProtocol;

    /// <summary>
    /// 命令码（可选，用于特定命令的解析配置）
    /// </summary>
    public byte? CommandCode { get; set; }

    /// <summary>
    /// 物理量定义列表
    /// </summary>
    public List<PhysicalQuantity> PhysicalQuantities { get; set; } = new();

    /// <summary>
    /// 数据映射配置列表
    /// </summary>
    public List<DataMappingConfig> DataMappings { get; set; } = new();

    /// <summary>
    /// 索引解析配置（用于支持idx的数据帧）
    /// </summary>
    public IndexParseConfig? IndexParseConfig { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 配置版本
    /// </summary>
    [Required]
    [StringLength(20, MinimumLength = 1, ErrorMessage = "版本长度必须在1-20字符之间")]
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者
    /// </summary>
    [StringLength(100, ErrorMessage = "创建者长度不能超过100字符")]
    public string CreatedBy { get; set; } = Environment.UserName;

    /// <summary>
    /// 最后修改者
    /// </summary>
    [StringLength(100, ErrorMessage = "修改者长度不能超过100字符")]
    public string LastModifiedBy { get; set; } = Environment.UserName;

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfig()
    {
        var errors = new List<string>();

        // 验证物理量名称唯一性
        var duplicateNames = PhysicalQuantities
            .GroupBy(pq => pq.Name)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        if (duplicateNames.Any())
        {
            errors.Add($"物理量名称重复: {string.Join(", ", duplicateNames)}");
        }

        // 验证数据映射配置
        foreach (var mapping in DataMappings)
        {
            var mappingValidation = mapping.ValidateConfig();
            if (!mappingValidation.IsValid)
            {
                errors.AddRange(mappingValidation.ErrorMessages.Select(e => $"映射 '{mapping.Name}': {e}"));
            }

            // 验证映射的物理量是否存在
            if (!PhysicalQuantities.Any(pq => pq.Name == mapping.PhysicalQuantityName))
            {
                errors.Add($"映射 '{mapping.Name}' 引用的物理量 '{mapping.PhysicalQuantityName}' 不存在");
            }
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            ErrorMessages = errors
        };
    }

    public override string ToString()
    {
        var cmdInfo = CommandCode.HasValue ? $" (CMD: 0x{CommandCode:X2})" : "";
        return $"{Name} - {ProtocolType}{cmdInfo}";
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> ErrorMessages { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> WarningMessages { get; set; } = new();

    public override string ToString()
    {
        if (IsValid)
            return "验证通过";

        return $"验证失败: {string.Join("; ", ErrorMessages)}";
    }
}
