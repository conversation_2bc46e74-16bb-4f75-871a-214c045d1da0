{"id": "example-commercial-001", "name": "商用内外机通讯协议示例配置", "description": "这是一个商用内外机通讯协议的完整配置示例，展示了如何配置字节级映射、位级映射和跨字节位拼接", "protocolType": "CommercialProtocol", "commandCode": 161, "physicalQuantities": [{"name": "IndoorTemperature", "displayName": "室内温度", "description": "室内环境温度传感器读数", "dataType": "Int16", "unit": "°C", "scaleFactor": 0.1, "offset": 0.0, "minValue": -40.0, "maxValue": 85.0, "decimalPlaces": 1, "isReadOnly": true, "groupName": "温度传感器", "sortOrder": 1, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "室内温度传感器，精度0.1°C"}, {"name": "OutdoorTemperature", "displayName": "室外温度", "description": "室外环境温度传感器读数", "dataType": "Int16", "unit": "°C", "scaleFactor": 0.1, "offset": 0.0, "minValue": -40.0, "maxValue": 85.0, "decimalPlaces": 1, "isReadOnly": true, "groupName": "温度传感器", "sortOrder": 2, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "室外温度传感器，精度0.1°C"}, {"name": "CompressorStatus", "displayName": "压缩机状态", "description": "压缩机运行状态指示", "dataType": "Boolean", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 1.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "设备状态", "sortOrder": 10, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "0=停止，1=运行"}, {"name": "FanSpeed", "displayName": "风机转速", "description": "室内风机转速等级", "dataType": "UInt8", "unit": "级", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 7.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "设备状态", "sortOrder": 11, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "0=停止，1-7=转速等级"}, {"name": "ErrorCode", "displayName": "故障代码", "description": "设备故障代码", "dataType": "UInt16", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 65535.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "故障信息", "sortOrder": 20, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "0=无故障，其他值表示具体故障代码"}, {"name": "SystemMode", "displayName": "系统模式", "description": "空调系统运行模式", "dataType": "UInt8", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 7.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "设备状态", "sortOrder": 12, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "0=关机，1=制冷，2=制热，3=除湿，4=送风，5=自动"}], "dataMappings": [{"id": "mapping-001", "name": "室内温度映射", "description": "将数据字节0-1映射到室内温度", "mappingType": "ByteMapping", "physicalQuantityName": "IndoorTemperature", "byteRange": {"startIndex": 0, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "bitPositions": [], "indexMapping": null, "isEnabled": true, "sortOrder": 1, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "mapping-002", "name": "室外温度映射", "description": "将数据字节2-3映射到室外温度", "mappingType": "ByteMapping", "physicalQuantityName": "OutdoorTemperature", "byteRange": {"startIndex": 2, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "bitPositions": [], "indexMapping": null, "isEnabled": true, "sortOrder": 2, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "mapping-003", "name": "压缩机状态映射", "description": "将数据字节4的第0位映射到压缩机状态", "mappingType": "BitMapping", "physicalQuantityName": "CompressorStatus", "byteRange": null, "bitPositions": [{"byteIndex": 4, "bitIndex": 0, "bitLength": 1, "targetBitOffset": 0}], "indexMapping": null, "isEnabled": true, "sortOrder": 3, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "mapping-004", "name": "风机转速映射", "description": "将数据字节4的第1-3位映射到风机转速", "mappingType": "BitMapping", "physicalQuantityName": "FanSpeed", "byteRange": null, "bitPositions": [{"byteIndex": 4, "bitIndex": 1, "bitLength": 3, "targetBitOffset": 0}], "indexMapping": null, "isEnabled": true, "sortOrder": 4, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "mapping-005", "name": "系统模式映射", "description": "将数据字节4的第4-6位映射到系统模式", "mappingType": "BitMapping", "physicalQuantityName": "SystemMode", "byteRange": null, "bitPositions": [{"byteIndex": 4, "bitIndex": 4, "bitLength": 3, "targetBitOffset": 0}], "indexMapping": null, "isEnabled": true, "sortOrder": 5, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "mapping-006", "name": "故障代码映射", "description": "将数据字节5-6映射到故障代码", "mappingType": "ByteMapping", "physicalQuantityName": "ErrorCode", "byteRange": {"startIndex": 5, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "bitPositions": [], "indexMapping": null, "isEnabled": true, "sortOrder": 6, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}], "indexParseConfig": null, "isEnabled": true, "version": "1.0.0", "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "createdBy": "System", "lastModifiedBy": "System"}