[{"name": "Temperature", "displayName": "温度", "description": "环境温度传感器读数", "dataType": "Int16", "unit": "°C", "scaleFactor": 0.1, "offset": 0, "minValue": -50, "maxValue": 100, "decimalPlaces": 1, "isReadOnly": true, "groupName": "环境参数", "sortOrder": 1, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "通用温度传感器"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "湿度", "description": "环境湿度传感器读数", "dataType": "UInt16", "unit": "%RH", "scaleFactor": 0.1, "offset": 0, "minValue": 0, "maxValue": 100, "decimalPlaces": 1, "isReadOnly": true, "groupName": "环境参数", "sortOrder": 2, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "相对湿度传感器"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "湿度", "description": "环境湿度传感器读数", "dataType": "UInt16", "unit": "%RH", "scaleFactor": 0.1, "offset": 0, "minValue": 0, "maxValue": 100, "decimalPlaces": 1, "isReadOnly": true, "groupName": "环境参数", "sortOrder": 2, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "相对湿度传感器"}]