using System.ComponentModel;
using System.Runtime.CompilerServices;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// ViewModel基类，提供属性更改通知和通用功能
/// </summary>
public abstract partial class ViewModelBase : ObservableObject, IDisposable
{
    private readonly ILoggingService? _loggingService;
    private bool _disposed = false;

    /// <summary>
    /// 是否正在执行操作
    /// </summary>
    [ObservableProperty]
    private bool _isBusy;

    /// <summary>
    /// 标题
    /// </summary>
    [ObservableProperty]
    private string _title = string.Empty;

    /// <summary>
    /// 日志服务
    /// </summary>
    protected ILoggingService? LoggingService => _loggingService;

    protected ViewModelBase()
    {
    }

    protected ViewModelBase(ILoggingService loggingService)
    {
        _loggingService = loggingService;
    }

    /// <summary>
    /// 初始化ViewModel
    /// </summary>
    public virtual async Task InitializeAsync()
    {
        try
        {
            _loggingService?.LogDebug("初始化ViewModel: {ViewModelType}", GetType().Name);
            await OnInitializeAsync();
        }
        catch (Exception ex)
        {
            _loggingService?.LogError(ex, "初始化ViewModel失败: {ViewModelType}", GetType().Name);
            throw;
        }
    }

    /// <summary>
    /// 子类重写此方法实现具体的初始化逻辑
    /// </summary>
    protected virtual Task OnInitializeAsync()
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 执行异步操作，自动处理忙状态和异常
    /// </summary>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称（用于日志）</param>
    protected async Task ExecuteAsync(Func<Task> operation, [CallerMemberName] string operationName = "")
    {
        if (IsBusy)
        {
            _loggingService?.LogWarning("操作正在进行中，忽略重复请求: {OperationName}", operationName);
            return;
        }

        try
        {
            IsBusy = true;
            using var scope = _loggingService?.BeginScope(operationName);
            await operation();
        }
        catch (Exception ex)
        {
            _loggingService?.LogError(ex, "执行操作失败: {OperationName}", operationName);
            await OnErrorAsync(ex, operationName);
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 执行异步操作并返回结果，自动处理忙状态和异常
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称（用于日志）</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, [CallerMemberName] string operationName = "")
    {
        if (IsBusy)
        {
            _loggingService?.LogWarning("操作正在进行中，忽略重复请求: {OperationName}", operationName);
            return default;
        }

        try
        {
            IsBusy = true;
            using var scope = _loggingService?.BeginScope(operationName);
            return await operation();
        }
        catch (Exception ex)
        {
            _loggingService?.LogError(ex, "执行操作失败: {OperationName}", operationName);
            await OnErrorAsync(ex, operationName);
            return default;
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 处理错误的虚方法，子类可以重写以实现自定义错误处理
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="operationName">操作名称</param>
    protected virtual Task OnErrorAsync(Exception exception, string operationName)
    {
        // 默认实现：记录错误日志
        _loggingService?.LogError(exception, "ViewModel操作出错: {OperationName}", operationName);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 验证属性值
    /// </summary>
    /// <param name="value">属性值</param>
    /// <param name="propertyName">属性名称</param>
    /// <returns>验证是否通过</returns>
    protected virtual bool ValidateProperty(object? value, [CallerMemberName] string propertyName = "")
    {
        // 子类可以重写此方法实现属性验证
        return true;
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 清理资源的虚方法
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _loggingService?.LogDebug("释放ViewModel资源: {ViewModelType}", GetType().Name);
            OnDispose();
            _disposed = true;
        }
    }

    /// <summary>
    /// 子类重写此方法实现具体的资源清理逻辑
    /// </summary>
    protected virtual void OnDispose()
    {
        // 子类实现具体的清理逻辑
    }
}
