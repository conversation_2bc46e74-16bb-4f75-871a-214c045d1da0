using AirMonitor.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Helpers;

/// <summary>
/// 协议解析配置验证器
/// </summary>
public class ProtocolParseConfigValidator
{
    private readonly ILogger<ProtocolParseConfigValidator>? _logger;

    public ProtocolParseConfigValidator(ILogger<ProtocolParseConfigValidator>? logger = null)
    {
        _logger = logger;
    }

    /// <summary>
    /// 验证协议解析配置的完整性
    /// </summary>
    /// <param name="config">要验证的配置</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateConfig(ProtocolParseConfig config)
    {
        var result = new ValidationResult();
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // 基本信息验证
            ValidateBasicInfo(config, errors);

            // 物理量定义验证
            ValidatePhysicalQuantities(config, errors, warnings);

            // 数据映射验证
            ValidateDataMappings(config, errors, warnings);

            // 索引配置验证
            ValidateIndexConfig(config, errors, warnings);

            // 交叉验证
            ValidateCrossReferences(config, errors, warnings);

            result.IsValid = errors.Count == 0;
            result.ErrorMessages = errors;
            result.WarningMessages = warnings;

            _logger?.LogDebug("配置验证完成: {ConfigName}, 有效={IsValid}, 错误={ErrorCount}, 警告={WarningCount}",
                config.Name, result.IsValid, errors.Count, warnings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "验证配置时发生异常: {ConfigName}", config.Name);
            
            result.IsValid = false;
            result.ErrorMessages = new List<string> { $"验证过程发生异常: {ex.Message}" };
            result.WarningMessages = warnings;
            
            return result;
        }
    }

    /// <summary>
    /// 测试配置解析功能
    /// </summary>
    /// <param name="config">配置</param>
    /// <param name="testData">测试数据</param>
    /// <returns>测试结果</returns>
    public ConfigTestResult TestConfig(ProtocolParseConfig config, byte[] testData)
    {
        var result = new ConfigTestResult
        {
            ConfigId = config.Id,
            ConfigName = config.Name,
            TestData = testData,
            TestTime = DateTime.Now
        };

        try
        {
            // 验证配置
            var validation = ValidateConfig(config);
            if (!validation.IsValid)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"配置验证失败: {string.Join("; ", validation.ErrorMessages)}";
                return result;
            }

            // 模拟数据解析
            var parsedValues = new List<PhysicalQuantityValue>();

            foreach (var mapping in config.DataMappings.Where(m => m.IsEnabled))
            {
                var physicalQuantity = config.PhysicalQuantities.FirstOrDefault(pq => pq.Name == mapping.PhysicalQuantityName);
                if (physicalQuantity == null)
                    continue;

                try
                {
                    var value = SimulateDataParsing(testData, mapping, physicalQuantity);
                    if (value != null)
                    {
                        parsedValues.Add(value);
                    }
                }
                catch (Exception ex)
                {
                    result.Warnings.Add($"映射 '{mapping.Name}' 解析失败: {ex.Message}");
                }
            }

            result.IsSuccess = true;
            result.ParsedValues = parsedValues;
            result.ParsedCount = parsedValues.Count;

            _logger?.LogDebug("配置测试完成: {ConfigName}, 成功={IsSuccess}, 解析数量={ParsedCount}",
                config.Name, result.IsSuccess, result.ParsedCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "测试配置时发生异常: {ConfigName}", config.Name);
            
            result.IsSuccess = false;
            result.ErrorMessage = $"测试过程发生异常: {ex.Message}";
            
            return result;
        }
    }

    /// <summary>
    /// 生成测试数据
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <param name="dataLength">数据长度</param>
    /// <returns>测试数据</returns>
    public byte[] GenerateTestData(DataPacketProtocolType protocolType, int dataLength = 20)
    {
        var random = new Random();
        var testData = new byte[dataLength];
        
        // 生成随机测试数据
        random.NextBytes(testData);
        
        // 根据协议类型设置一些特定的值
        switch (protocolType)
        {
            case DataPacketProtocolType.CommercialProtocol:
                // 设置一些典型的商用协议数据
                if (dataLength >= 2) // 温度数据 (25.6°C)
                {
                    var temp = (short)(256); // 25.6 * 10
                    testData[0] = (byte)(temp & 0xFF);
                    testData[1] = (byte)((temp >> 8) & 0xFF);
                }
                if (dataLength >= 5) // 状态字节
                {
                    testData[4] = 0x15; // 压缩机开启，风机3级，制冷模式
                }
                break;

            case DataPacketProtocolType.ModuleProtocol:
                // 设置一些典型的模块协议数据
                if (dataLength >= 2) // 索引值
                {
                    testData[0] = 0x01; // 索引低字节
                    testData[1] = 0x00; // 索引高字节
                }
                if (dataLength >= 6) // 数据值
                {
                    var value = (short)(235); // 23.5°C
                    testData[4] = (byte)(value & 0xFF);
                    testData[5] = (byte)((value >> 8) & 0xFF);
                }
                break;
        }

        return testData;
    }

    #region 私有方法

    private void ValidateBasicInfo(ProtocolParseConfig config, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(config.Name))
            errors.Add("配置名称不能为空");

        if (string.IsNullOrWhiteSpace(config.Version))
            errors.Add("配置版本不能为空");

        if (config.ProtocolType == DataPacketProtocolType.AutoDetect)
            errors.Add("协议类型不能为自动识别");
    }

    private void ValidatePhysicalQuantities(ProtocolParseConfig config, List<string> errors, List<string> warnings)
    {
        if (config.PhysicalQuantities.Count == 0)
        {
            warnings.Add("配置中没有定义物理量");
            return;
        }

        var names = new HashSet<string>();
        foreach (var pq in config.PhysicalQuantities)
        {
            if (string.IsNullOrWhiteSpace(pq.Name))
            {
                errors.Add("物理量名称不能为空");
                continue;
            }

            if (!names.Add(pq.Name))
            {
                errors.Add($"物理量名称重复: {pq.Name}");
            }

            if (string.IsNullOrWhiteSpace(pq.DisplayName))
                warnings.Add($"物理量 '{pq.Name}' 缺少显示名称");

            if (pq.ScaleFactor == 0)
                warnings.Add($"物理量 '{pq.Name}' 的缩放因子为0，可能导致计算错误");

            if (pq.MinValue.HasValue && pq.MaxValue.HasValue && pq.MinValue.Value >= pq.MaxValue.Value)
                errors.Add($"物理量 '{pq.Name}' 的最小值大于等于最大值");
        }
    }

    private void ValidateDataMappings(ProtocolParseConfig config, List<string> errors, List<string> warnings)
    {
        if (config.DataMappings.Count == 0)
        {
            warnings.Add("配置中没有定义数据映射");
            return;
        }

        foreach (var mapping in config.DataMappings)
        {
            if (string.IsNullOrWhiteSpace(mapping.Name))
                errors.Add("数据映射名称不能为空");

            if (string.IsNullOrWhiteSpace(mapping.PhysicalQuantityName))
                errors.Add($"映射 '{mapping.Name}' 没有指定物理量");

            var mappingValidation = mapping.ValidateConfig();
            if (!mappingValidation.IsValid)
            {
                errors.AddRange(mappingValidation.ErrorMessages.Select(e => $"映射 '{mapping.Name}': {e}"));
            }
        }
    }

    private void ValidateIndexConfig(ProtocolParseConfig config, List<string> errors, List<string> warnings)
    {
        if (config.IndexParseConfig == null)
            return;

        var indexConfig = config.IndexParseConfig;

        if (indexConfig.IndexByteRange == null)
            errors.Add("索引配置缺少索引字节范围定义");

        if (indexConfig.ParseIndexAsPhysicalQuantity)
        {
            if (string.IsNullOrWhiteSpace(indexConfig.IndexHighBytePhysicalQuantityName) &&
                string.IsNullOrWhiteSpace(indexConfig.IndexLowBytePhysicalQuantityName))
            {
                warnings.Add("启用了索引值解析但未指定对应的物理量");
            }
        }
    }

    private void ValidateCrossReferences(ProtocolParseConfig config, List<string> errors, List<string> warnings)
    {
        var physicalQuantityNames = config.PhysicalQuantities.Select(pq => pq.Name).ToHashSet();

        foreach (var mapping in config.DataMappings)
        {
            if (!physicalQuantityNames.Contains(mapping.PhysicalQuantityName))
            {
                errors.Add($"映射 '{mapping.Name}' 引用的物理量 '{mapping.PhysicalQuantityName}' 不存在");
            }
        }

        if (config.IndexParseConfig != null)
        {
            if (!string.IsNullOrWhiteSpace(config.IndexParseConfig.IndexHighBytePhysicalQuantityName) &&
                !physicalQuantityNames.Contains(config.IndexParseConfig.IndexHighBytePhysicalQuantityName))
            {
                errors.Add($"索引配置引用的高字节物理量 '{config.IndexParseConfig.IndexHighBytePhysicalQuantityName}' 不存在");
            }

            if (!string.IsNullOrWhiteSpace(config.IndexParseConfig.IndexLowBytePhysicalQuantityName) &&
                !physicalQuantityNames.Contains(config.IndexParseConfig.IndexLowBytePhysicalQuantityName))
            {
                errors.Add($"索引配置引用的低字节物理量 '{config.IndexParseConfig.IndexLowBytePhysicalQuantityName}' 不存在");
            }
        }
    }

    private PhysicalQuantityValue? SimulateDataParsing(byte[] testData, DataMappingConfig mapping, PhysicalQuantity physicalQuantity)
    {
        object? rawValue = null;

        switch (mapping.MappingType)
        {
            case DataMappingType.ByteMapping:
                if (mapping.ByteRange != null)
                {
                    rawValue = ExtractByteRangeValue(testData, mapping.ByteRange, physicalQuantity.DataType);
                }
                break;

            case DataMappingType.BitMapping:
                if (mapping.BitPositions.Count > 0)
                {
                    rawValue = ExtractBitValue(testData, mapping.BitPositions[0]);
                }
                break;

            case DataMappingType.CrossByteBitMapping:
                if (mapping.BitPositions.Count > 1)
                {
                    rawValue = ExtractCrossByteBitValue(testData, mapping.BitPositions, physicalQuantity.DataType);
                }
                break;

            case DataMappingType.IndexMapping:
                // 简化的索引映射模拟
                if (mapping.IndexMapping?.DataByteRange != null)
                {
                    rawValue = ExtractByteRangeValue(testData, mapping.IndexMapping.DataByteRange, physicalQuantity.DataType);
                }
                break;
        }

        if (rawValue != null)
        {
            var actualValue = physicalQuantity.CalculateActualValue(Convert.ToDouble(rawValue));
            var isValid = physicalQuantity.IsValueValid(actualValue);

            return new PhysicalQuantityValue
            {
                PhysicalQuantityName = physicalQuantity.Name,
                RawValue = rawValue,
                ActualValue = actualValue,
                FormattedValue = physicalQuantity.FormatValue(actualValue),
                IsValid = isValid,
                Timestamp = DateTime.Now,
                SourceInfo = $"测试映射: {mapping.Name}"
            };
        }

        return null;
    }

    private object? ExtractByteRangeValue(byte[] data, ByteRange byteRange, DataValueType dataType)
    {
        if (data.Length <= byteRange.StartIndex || byteRange.StartIndex < 0)
            return null;

        var endIndex = byteRange.StartIndex + byteRange.Length;
        if (endIndex > data.Length)
            return null;

        var bytes = new byte[byteRange.Length];
        Array.Copy(data, byteRange.StartIndex, bytes, 0, byteRange.Length);

        // 简化的数据类型转换
        return dataType switch
        {
            DataValueType.UInt8 => bytes.Length >= 1 ? bytes[0] : (byte)0,
            DataValueType.Int8 => bytes.Length >= 1 ? (sbyte)bytes[0] : (sbyte)0,
            DataValueType.UInt16 => bytes.Length >= 2 ? BitConverter.ToUInt16(bytes, 0) : (ushort)0,
            DataValueType.Int16 => bytes.Length >= 2 ? BitConverter.ToInt16(bytes, 0) : (short)0,
            DataValueType.Boolean => bytes.Length >= 1 && bytes[0] != 0,
            _ => 0
        };
    }

    private int ExtractBitValue(byte[] data, BitPosition bitPosition)
    {
        if (data.Length <= bitPosition.ByteIndex || bitPosition.ByteIndex < 0)
            return 0;

        var byteValue = data[bitPosition.ByteIndex];
        var mask = (1 << bitPosition.BitLength) - 1;
        return (byteValue >> bitPosition.BitIndex) & mask;
    }

    private object? ExtractCrossByteBitValue(byte[] data, List<BitPosition> bitPositions, DataValueType dataType)
    {
        uint result = 0;

        foreach (var bitPos in bitPositions.OrderBy(bp => bp.TargetBitOffset))
        {
            if (data.Length <= bitPos.ByteIndex || bitPos.ByteIndex < 0)
                continue;

            var byteValue = data[bitPos.ByteIndex];
            var mask = (1u << bitPos.BitLength) - 1;
            var extractedBits = (uint)((byteValue >> bitPos.BitIndex) & mask);
            result |= extractedBits << bitPos.TargetBitOffset;
        }

        return dataType switch
        {
            DataValueType.UInt8 => (byte)result,
            DataValueType.UInt16 => (ushort)result,
            DataValueType.UInt32 => result,
            _ => (int)result
        };
    }

    #endregion
}

/// <summary>
/// 配置测试结果
/// </summary>
public class ConfigTestResult
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public string ConfigId { get; set; } = string.Empty;

    /// <summary>
    /// 配置名称
    /// </summary>
    public string ConfigName { get; set; } = string.Empty;

    /// <summary>
    /// 测试数据
    /// </summary>
    public byte[] TestData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 是否测试成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 解析得到的物理量值列表
    /// </summary>
    public List<PhysicalQuantityValue> ParsedValues { get; set; } = new();

    /// <summary>
    /// 解析的物理量数量
    /// </summary>
    public int ParsedCount { get; set; }

    /// <summary>
    /// 测试时间
    /// </summary>
    public DateTime TestTime { get; set; }

    public override string ToString()
    {
        return $"测试结果: {ConfigName} - {(IsSuccess ? "成功" : "失败")} ({ParsedCount} 个物理量)";
    }
}
