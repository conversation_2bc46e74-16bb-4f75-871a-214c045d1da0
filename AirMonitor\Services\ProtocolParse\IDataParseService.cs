using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 数据解析服务接口
/// </summary>
public interface IDataParseService
{
    #region 数据解析

    /// <summary>
    /// 解析数据包为物理量值列表
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>物理量值列表</returns>
    Task<List<PhysicalQuantityValue>> ParseDataPacketAsync(DataPacketBase packet);

    /// <summary>
    /// 根据指定配置解析数据包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <param name="configId">配置ID</param>
    /// <returns>物理量值列表</returns>
    Task<List<PhysicalQuantityValue>> ParseDataPacketWithConfigAsync(DataPacketBase packet, string configId);

    /// <summary>
    /// 解析原始数据为物理量值列表
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="protocolType">协议类型</param>
    /// <param name="commandCode">命令码（可选）</param>
    /// <returns>物理量值列表</returns>
    Task<List<PhysicalQuantityValue>> ParseRawDataAsync(byte[] rawData, DataPacketProtocolType protocolType, byte? commandCode = null);

    /// <summary>
    /// 根据数据映射配置解析数据
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="mappingConfig">数据映射配置</param>
    /// <param name="physicalQuantity">物理量定义</param>
    /// <returns>物理量值，如果解析失败则返回null</returns>
    Task<PhysicalQuantityValue?> ParseDataWithMappingAsync(byte[] rawData, DataMappingConfig mappingConfig, PhysicalQuantity physicalQuantity);

    #endregion

    #region 索引数据解析

    /// <summary>
    /// 解析索引数据帧
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <param name="indexParseConfig">索引解析配置</param>
    /// <returns>解析结果（索引信息和物理量值列表）</returns>
    Task<IndexDataParseResult> ParseIndexDataAsync(DataPacketBase packet, IndexParseConfig indexParseConfig);

    /// <summary>
    /// 根据索引值获取对应的数据映射配置
    /// </summary>
    /// <param name="indexValue">索引值</param>
    /// <param name="protocolType">协议类型</param>
    /// <param name="commandCode">命令码</param>
    /// <returns>数据映射配置列表</returns>
    Task<List<DataMappingConfig>> GetMappingConfigsByIndexAsync(int indexValue, DataPacketProtocolType protocolType, byte commandCode);

    #endregion

    #region 数据提取

    /// <summary>
    /// 从原始数据中提取字节范围的值
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="byteRange">字节范围</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>提取的值</returns>
    object? ExtractByteRangeValue(byte[] rawData, ByteRange byteRange, DataValueType dataType);

    /// <summary>
    /// 从原始数据中提取位值
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="bitPosition">位位置</param>
    /// <returns>位值（0或1）</returns>
    int ExtractBitValue(byte[] rawData, BitPosition bitPosition);

    /// <summary>
    /// 从原始数据中提取跨字节位拼接的值
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="bitPositions">位位置列表</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>拼接后的值</returns>
    object? ExtractCrossByteBitValue(byte[] rawData, List<BitPosition> bitPositions, DataValueType dataType);

    #endregion

    #region 数据转换

    /// <summary>
    /// 将原始值转换为指定数据类型
    /// </summary>
    /// <param name="rawValue">原始值</param>
    /// <param name="dataType">目标数据类型</param>
    /// <returns>转换后的值</returns>
    object? ConvertToDataType(object rawValue, DataValueType dataType);

    /// <summary>
    /// 将字节数组转换为指定数据类型的值
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="byteOrder">字节序</param>
    /// <returns>转换后的值</returns>
    object? ConvertBytesToValue(byte[] bytes, DataValueType dataType, ByteOrder byteOrder = ByteOrder.LittleEndian);

    #endregion

    #region 配置管理

    /// <summary>
    /// 设置活动的解析配置
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <param name="configIds">配置ID列表</param>
    /// <returns>设置是否成功</returns>
    Task<bool> SetActiveParseConfigsAsync(DataPacketProtocolType protocolType, List<string> configIds);

    /// <summary>
    /// 获取活动的解析配置
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <returns>活动配置列表</returns>
    Task<List<ProtocolParseConfig>> GetActiveParseConfigsAsync(DataPacketProtocolType protocolType);

    /// <summary>
    /// 刷新解析配置缓存
    /// </summary>
    /// <returns>刷新是否成功</returns>
    Task<bool> RefreshParseConfigsAsync();

    #endregion

    #region 事件

    /// <summary>
    /// 数据解析完成事件
    /// </summary>
    event EventHandler<DataParseCompletedEventArgs>? DataParseCompleted;

    /// <summary>
    /// 数据解析错误事件
    /// </summary>
    event EventHandler<DataParseErrorEventArgs>? DataParseError;

    #endregion
}

/// <summary>
/// 索引数据解析结果
/// </summary>
public class IndexDataParseResult
{
    /// <summary>
    /// 索引值
    /// </summary>
    public int IndexValue { get; set; }

    /// <summary>
    /// 索引数量（如果有）
    /// </summary>
    public int? IndexCount { get; set; }

    /// <summary>
    /// 索引相关的物理量值列表
    /// </summary>
    public List<PhysicalQuantityValue> IndexPhysicalQuantities { get; set; } = new();

    /// <summary>
    /// 数据相关的物理量值列表
    /// </summary>
    public List<PhysicalQuantityValue> DataPhysicalQuantities { get; set; } = new();

    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 获取所有物理量值
    /// </summary>
    /// <returns>所有物理量值列表</returns>
    public List<PhysicalQuantityValue> GetAllPhysicalQuantities()
    {
        var result = new List<PhysicalQuantityValue>();
        result.AddRange(IndexPhysicalQuantities);
        result.AddRange(DataPhysicalQuantities);
        return result;
    }
}

/// <summary>
/// 数据解析完成事件参数
/// </summary>
public class DataParseCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 原始数据包
    /// </summary>
    public DataPacketBase Packet { get; set; } = null!;

    /// <summary>
    /// 解析得到的物理量值列表
    /// </summary>
    public List<PhysicalQuantityValue> PhysicalQuantities { get; set; } = new();

    /// <summary>
    /// 使用的配置ID
    /// </summary>
    public string? ConfigId { get; set; }

    /// <summary>
    /// 解析耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 解析时间
    /// </summary>
    public DateTime ParseTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 数据解析错误事件参数
/// </summary>
public class DataParseErrorEventArgs : EventArgs
{
    /// <summary>
    /// 原始数据包
    /// </summary>
    public DataPacketBase? Packet { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常对象
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 使用的配置ID
    /// </summary>
    public string? ConfigId { get; set; }

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime ErrorTime { get; set; } = DateTime.Now;
}
