using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AirMonitor.Models;

/// <summary>
/// 数据映射类型枚举
/// </summary>
public enum DataMappingType
{
    /// <summary>
    /// 字节级映射：单个或多个连续字节映射到一个物理量
    /// </summary>
    [Description("字节级映射")]
    ByteMapping = 0,

    /// <summary>
    /// 位级映射：字节中的特定位映射到一个物理量
    /// </summary>
    [Description("位级映射")]
    BitMapping = 1,

    /// <summary>
    /// 跨字节位拼接：不相邻字节中的特定位组合拼接成一个物理量
    /// </summary>
    [Description("跨字节位拼接")]
    CrossByteBitMapping = 2,

    /// <summary>
    /// 基于索引(idx)的映射：根据帧中的idx值动态映射
    /// </summary>
    [Description("索引映射")]
    IndexMapping = 3
}

/// <summary>
/// 数据类型枚举
/// </summary>
public enum DataValueType
{
    /// <summary>
    /// 无符号8位整数
    /// </summary>
    [Description("UInt8")]
    UInt8 = 0,

    /// <summary>
    /// 有符号8位整数
    /// </summary>
    [Description("Int8")]
    Int8 = 1,

    /// <summary>
    /// 无符号16位整数
    /// </summary>
    [Description("UInt16")]
    UInt16 = 2,

    /// <summary>
    /// 有符号16位整数
    /// </summary>
    [Description("Int16")]
    Int16 = 3,

    /// <summary>
    /// 无符号32位整数
    /// </summary>
    [Description("UInt32")]
    UInt32 = 4,

    /// <summary>
    /// 有符号32位整数
    /// </summary>
    [Description("Int32")]
    Int32 = 5,

    /// <summary>
    /// 单精度浮点数
    /// </summary>
    [Description("Float")]
    Float = 6,

    /// <summary>
    /// 双精度浮点数
    /// </summary>
    [Description("Double")]
    Double = 7,

    /// <summary>
    /// 布尔值
    /// </summary>
    [Description("Boolean")]
    Boolean = 8,

    /// <summary>
    /// 字符串
    /// </summary>
    [Description("String")]
    String = 9
}

/// <summary>
/// 字节序枚举
/// </summary>
public enum ByteOrder
{
    /// <summary>
    /// 小端序（低字节在前）
    /// </summary>
    [Description("小端序")]
    LittleEndian = 0,

    /// <summary>
    /// 大端序（高字节在前）
    /// </summary>
    [Description("大端序")]
    BigEndian = 1
}

/// <summary>
/// 位位置定义
/// </summary>
public class BitPosition
{
    /// <summary>
    /// 字节索引（从0开始）
    /// </summary>
    [Required]
    [Range(0, int.MaxValue, ErrorMessage = "字节索引必须大于等于0")]
    public int ByteIndex { get; set; }

    /// <summary>
    /// 位索引（0-7，0表示最低位）
    /// </summary>
    [Required]
    [Range(0, 7, ErrorMessage = "位索引必须在0-7之间")]
    public int BitIndex { get; set; }

    /// <summary>
    /// 位长度（用于跨位拼接）
    /// </summary>
    [Range(1, 8, ErrorMessage = "位长度必须在1-8之间")]
    public int BitLength { get; set; } = 1;

    /// <summary>
    /// 在目标值中的位偏移（用于跨字节位拼接）
    /// </summary>
    [Range(0, 31, ErrorMessage = "位偏移必须在0-31之间")]
    public int TargetBitOffset { get; set; } = 0;

    public override string ToString()
    {
        return $"Byte[{ByteIndex}].Bit[{BitIndex}:{BitIndex + BitLength - 1}]";
    }
}

/// <summary>
/// 字节范围定义
/// </summary>
public class ByteRange
{
    /// <summary>
    /// 起始字节索引（从0开始）
    /// </summary>
    [Required]
    [Range(0, int.MaxValue, ErrorMessage = "起始字节索引必须大于等于0")]
    public int StartIndex { get; set; }

    /// <summary>
    /// 字节长度
    /// </summary>
    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "字节长度必须大于0")]
    public int Length { get; set; }

    /// <summary>
    /// 字节序
    /// </summary>
    public ByteOrder ByteOrder { get; set; } = ByteOrder.LittleEndian;

    public override string ToString()
    {
        return $"Bytes[{StartIndex}:{StartIndex + Length - 1}]({ByteOrder})";
    }
}

/// <summary>
/// 索引解析配置
/// </summary>
public class IndexParseConfig
{
    /// <summary>
    /// 索引字节位置
    /// </summary>
    [Required]
    public ByteRange IndexByteRange { get; set; } = new();

    /// <summary>
    /// 索引数量字节位置（可选）
    /// </summary>
    public ByteRange? IndexCountByteRange { get; set; }

    /// <summary>
    /// 是否解析索引值本身为物理量
    /// </summary>
    public bool ParseIndexAsPhysicalQuantity { get; set; } = false;

    /// <summary>
    /// 索引值高8位解析配置（可选）
    /// </summary>
    public string? IndexHighBytePhysicalQuantityName { get; set; }

    /// <summary>
    /// 索引值低8位解析配置（可选）
    /// </summary>
    public string? IndexLowBytePhysicalQuantityName { get; set; }

    /// <summary>
    /// 索引值特定位解析配置列表（可选）
    /// </summary>
    public List<IndexBitParseConfig> IndexBitParseConfigs { get; set; } = new();
}

/// <summary>
/// 索引位解析配置
/// </summary>
public class IndexBitParseConfig
{
    /// <summary>
    /// 位位置
    /// </summary>
    [Required]
    public BitPosition BitPosition { get; set; } = new();

    /// <summary>
    /// 对应的物理量名称
    /// </summary>
    [Required]
    public string PhysicalQuantityName { get; set; } = string.Empty;
}
