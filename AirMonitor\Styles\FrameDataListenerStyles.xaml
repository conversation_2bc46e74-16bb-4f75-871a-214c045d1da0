<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 帧数据监听窗体样式 -->
    <Style x:Key="FrameDataListenerWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="#F5F5F5" />
        <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
        <Setter Property="FontSize" Value="12" />
        <!-- 确保窗体不会始终置顶 -->
        <Setter Property="Topmost" Value="False" />
        <!-- 允许窗体在任务栏中显示 -->
        <Setter Property="ShowInTaskbar" Value="True" />
        <!-- 设置窗体图标（可选） -->
        <Setter Property="WindowStyle" Value="SingleBorderWindow" />
        <!-- 允许调整大小 -->
        <Setter Property="ResizeMode" Value="CanResize" />
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="FrameDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="GridLinesVisibility" Value="Horizontal" />
        <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0" />
        <Setter Property="RowBackground" Value="White" />
        <Setter Property="AlternatingRowBackground" Value="#F9F9F9" />
        <Setter Property="HeadersVisibility" Value="Column" />
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="CanUserAddRows" Value="False" />
        <Setter Property="CanUserDeleteRows" Value="False" />
        <Setter Property="IsReadOnly" Value="True" />
        <Setter Property="SelectionMode" Value="Extended" />
        <Setter Property="SelectionUnit" Value="FullRow" />
        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace" />
        <Setter Property="FontSize" Value="11" />
    </Style>

    <!-- 数据网格列头样式 -->
    <Style x:Key="FrameDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="#F8F8F8" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,0,1,1" />
        <Setter Property="Padding" Value="8,6" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>

    <!-- 数据网格行样式 -->
    <Style x:Key="FrameDataGridRowStyle" TargetType="DataGridRow">
        <Setter Property="MinHeight" Value="24" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#E8F4FD" />
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="#CCE8FF" />
                <Setter Property="BorderBrush" Value="#66AAFF" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 数据网格单元格样式 -->
    <Style x:Key="FrameDataGridCellStyle" TargetType="DataGridCell">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="Foreground" Value="Black" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 控制按钮样式 -->
    <Style x:Key="ControlButtonStyle" TargetType="Button">
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Margin" Value="5,0" />
        <Setter Property="MinWidth" Value="80" />
        <Setter Property="Background" Value="#F0F0F0" />
        <Setter Property="BorderBrush" Value="#D0D0D0" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#E0E0E0" />
                <Setter Property="BorderBrush" Value="#C0C0C0" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#D0D0D0" />
                <Setter Property="BorderBrush" Value="#B0B0B0" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.6" />
                <Setter Property="Cursor" Value="Arrow" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 主要操作按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ControlButtonStyle}">
        <Setter Property="Background" Value="#007ACC" />
        <Setter Property="Foreground" Value="White" />
        <Setter Property="BorderBrush" Value="#005A9B" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#005A9B" />
                <Setter Property="BorderBrush" Value="#004578" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#004578" />
                <Setter Property="BorderBrush" Value="#003456" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="ControlCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Margin" Value="5,0" />
        <Setter Property="Cursor" Value="Hand" />
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="ControlTextBoxStyle" TargetType="TextBox">
        <Setter Property="Padding" Value="4,2" />
        <Setter Property="BorderBrush" Value="#D0D0D0" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="White" />
        <Setter Property="FontSize" Value="12" />
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="#007ACC" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="#C0C0C0" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 状态文本样式 -->
    <Style x:Key="StatusTextStyle" TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Margin" Value="5,0" />
        <Setter Property="Foreground" Value="#333333" />
    </Style>

    <!-- 标签文本样式 -->
    <Style x:Key="LabelTextStyle" TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Margin" Value="5,0" />
        <Setter Property="Foreground" Value="#666666" />
        <Setter Property="FontWeight" Value="SemiBold" />
    </Style>

    <!-- 控制面板样式 -->
    <Style x:Key="ControlPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F8F8" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="Padding" Value="10" />
    </Style>

    <!-- 状态栏样式 -->
    <Style x:Key="StatusBarStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F8F8" />
        <Setter Property="BorderBrush" Value="#E0E0E0" />
        <Setter Property="BorderThickness" Value="0,1,0,0" />
        <Setter Property="Padding" Value="10,5" />
    </Style>

</ResourceDictionary>
