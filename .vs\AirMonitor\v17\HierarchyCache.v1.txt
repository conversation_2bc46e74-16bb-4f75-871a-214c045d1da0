﻿++解决方案 'AirMonitor' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:AirMonitor.sln
++AirMonitor
i:{00000000-0000-0000-0000-000000000000}:AirMonitor
++依赖项
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>51
++包
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>156
++CommunityToolkit.Mvvm (8.4.0)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>159
++Microsoft.Extensions.Configuration (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>163
++Microsoft.Extensions.Configuration.Json (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>164
++Microsoft.Extensions.DependencyInjection (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>162
++Microsoft.Extensions.Hosting (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>167
++Serilog (4.3.0)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>157
++Serilog.Enrichers.Environment (3.0.1)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>342
++Serilog.Enrichers.Thread (4.0.0)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>333
++Serilog.Extensions.Logging (9.0.2)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>188
++Serilog.Sinks.File (7.0.0)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>158
++System.IO.Ports (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>1368
++System.Management (9.0.6)
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>1373
++分析器
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>86
++框架
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>95
++Controls
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\controls\
++ModalOverlay.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\controls\modaloverlay.xaml
++Converters
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\converters\
++BooleanConverters.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\converters\booleanconverters.cs
++Helpers
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\helpers\
++CommandHelper.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\helpers\commandhelper.cs
++CrcHelper.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\helpers\crchelper.cs
++LoggingHelper.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\helpers\logginghelper.cs
++Logs
++Models
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\
++AppSettings.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\appsettings.cs
++DataPacketModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\datapacketmodels.cs
++FrameDataModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\framedatamodels.cs
++SerialPortModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\serialportmodels.cs
++Services
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\
++Config
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\config\
++ConfigurationService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\config\configurationservice.cs
++IConfigurationService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\config\iconfigurationservice.cs
++DataBridge
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\databridge\
++DataBridgeService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\databridge\databridgeservice.cs
++IDataBridgeService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\databridge\idatabridgeservice.cs
++DataPacket
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\datapacket\
++DataPacketService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\datapacket\datapacketservice.cs
++IDataPacketService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\datapacket\idatapacketservice.cs
++Dialog
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\dialog\
++DialogService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\dialog\dialogservice.cs
++IDialogService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\dialog\idialogservice.cs
++Log
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\log\
++ILoggingService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\log\iloggingservice.cs
++LoggingService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\log\loggingservice.cs
++SerialPort
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\serialport\
++ISerialPortService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\serialport\iserialportservice.cs
++SerialPortService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\serialport\serialportservice.cs
++ServiceContainer.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\servicecontainer.cs
++Styles
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\styles\
++FrameDataListenerStyles.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\styles\framedatalistenerstyles.xaml
++MainWindowStyles.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\styles\mainwindowstyles.xaml
++Tests
++ViewModels
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\
++FrameDataListenerViewModel.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\framedatalistenerviewmodel.cs
++MainViewModel.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\mainviewmodel.cs
++SerialPortConnectionViewModel.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\serialportconnectionviewmodel.cs
++ViewModelBase.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\viewmodelbase.cs
++ViewModelLocator.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\viewmodellocator.cs
++Views
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\
++DialogWindowBase.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\dialogwindowbase.xaml
++FrameDataListenerWindow.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\framedatalistenerwindow.xaml
++FrameDataListenerWindow.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\framedatalistenerwindow.xaml.cs
++SerialPortConnectionDialog.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\serialportconnectiondialog.xaml
++App.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\app.xaml
++App.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\app.xaml.cs
++appsettings.json
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\appsettings.json
++AssemblyInfo.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\assemblyinfo.cs
++MainWindow.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\mainwindow.xaml
++MainWindow.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\mainwindow.xaml.cs
++CommunityToolkit.Mvvm.CodeFixers
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\analyzers\dotnet\roslyn4.12\cs\communitytoolkit.mvvm.codefixers.dll
++CommunityToolkit.Mvvm.SourceGenerators
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\analyzers\dotnet\roslyn4.12\cs\communitytoolkit.mvvm.sourcegenerators.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\sdk\9.0.300\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\sdk\9.0.300\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{4496c365-ed66-47cf-8050-4b2c36231928}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.5\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>97
++Microsoft.WindowsDesktop.App.WPF
i:{4496c365-ed66-47cf-8050-4b2c36231928}:>96
++ModalOverlay.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\controls\modaloverlay.xaml.cs
++app-20250624.log
++CrcValidationTest.cs
++DialogWindowBase.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\dialogwindowbase.xaml.cs
++SerialPortConnectionDialog.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\serialportconnectiondialog.xaml.cs
++Docs
++WindowLayerManagementTest.md
++ProtocolParseModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\protocolparsemodels.cs
++PhysicalQuantityModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\physicalquantitymodels.cs
++DataMappingModels.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\models\datamappingmodels.cs
++ProtocolParse
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\protocolparse\
++IProtocolParseConfigService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\protocolparse\iprotocolparseconfigservice.cs
++ProtocolParseConfigService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\protocolparse\protocolparseconfigservice.cs
++IDataParseService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\protocolparse\idataparseservice.cs
++ProtocolParseConfigViewModel.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\viewmodels\protocolparseconfigviewmodel.cs
++ProtocolParseConfigWindow.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\protocolparseconfigwindow.xaml
++ProtocolParseConfigWindow.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\views\protocolparseconfigwindow.xaml.cs
++BytePositionSelector.xaml
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\controls\bytepositionselector.xaml
++BytePositionSelector.xaml.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\controls\bytepositionselector.xaml.cs
++ProtocolParseConverters.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\converters\protocolparseconverters.cs
++DataParseService.cs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\services\protocolparse\dataparseservice.cs
++Configs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\
++ProtocolParseConfigs
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\protocolparseconfigs\
++example-commercial-protocol.json
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\protocolparseconfigs\example-commercial-protocol.json
++example-module-protocol.json
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\protocolparseconfigs\example-module-protocol.json
++PhysicalQuantities
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\physicalquantities\
++default-quantities.json
i:{4496c365-ed66-47cf-8050-4b2c36231928}:d:\00 airmonitor\airmonitor\configs\physicalquantities\default-quantities.json
