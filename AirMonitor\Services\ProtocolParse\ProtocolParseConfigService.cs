using AirMonitor.Models;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AirMonitor.Services;

/// <summary>
/// 协议数据解析配置服务实现
/// </summary>
public class ProtocolParseConfigService : IProtocolParseConfigService, IDisposable
{
    #region 字段

    private readonly ILogger<ProtocolParseConfigService> _logger;
    private readonly string _configDirectory;
    private readonly string _physicalQuantityDirectory;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly object _configLock = new();
    private readonly object _physicalQuantityLock = new();
    
    private List<ProtocolParseConfig> _configs = new();
    private List<PhysicalQuantity> _physicalQuantities = new();
    private bool _disposed = false;

    #endregion

    #region 构造函数

    public ProtocolParseConfigService(ILogger<ProtocolParseConfigService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 设置配置文件目录
        _configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", "ProtocolParseConfigs");
        _physicalQuantityDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", "PhysicalQuantities");
        
        // 确保目录存在
        Directory.CreateDirectory(_configDirectory);
        Directory.CreateDirectory(_physicalQuantityDirectory);

        // 配置JSON序列化选项
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        // 初始化时加载配置
        _ = Task.Run(async () =>
        {
            await LoadAllConfigsAsync();
            await LoadAllPhysicalQuantitiesAsync();
        });

        _logger.LogInformation("协议数据解析配置服务已初始化");
    }

    #endregion

    #region 事件

    public event EventHandler<ProtocolParseConfigChangedEventArgs>? ConfigChanged;
    public event EventHandler<PhysicalQuantityChangedEventArgs>? PhysicalQuantityChanged;

    #endregion

    #region 配置管理

    public async Task<List<ProtocolParseConfig>> GetAllConfigsAsync()
    {
        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.ToList();
        }
    }

    public async Task<ProtocolParseConfig?> GetConfigByIdAsync(string configId)
    {
        if (string.IsNullOrEmpty(configId))
            return null;

        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.FirstOrDefault(c => c.Id == configId);
        }
    }

    public async Task<List<ProtocolParseConfig>> GetConfigsByProtocolTypeAsync(DataPacketProtocolType protocolType)
    {
        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.Where(c => c.ProtocolType == protocolType).ToList();
        }
    }

    public async Task<ProtocolParseConfig?> GetConfigByProtocolAndCommandAsync(DataPacketProtocolType protocolType, byte commandCode)
    {
        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.FirstOrDefault(c => 
                c.ProtocolType == protocolType && 
                c.CommandCode == commandCode);
        }
    }

    public async Task<bool> SaveConfigAsync(ProtocolParseConfig config)
    {
        if (config == null)
        {
            _logger.LogWarning("尝试保存空的配置对象");
            return false;
        }

        try
        {
            // 验证配置
            var validation = await ValidateConfigAsync(config);
            if (!validation.IsValid)
            {
                _logger.LogWarning("配置验证失败: {Errors}", string.Join("; ", validation.ErrorMessages));
                return false;
            }

            var isNewConfig = false;
            lock (_configLock)
            {
                var existingIndex = _configs.FindIndex(c => c.Id == config.Id);
                if (existingIndex >= 0)
                {
                    config.LastModifiedTime = DateTime.Now;
                    config.LastModifiedBy = Environment.UserName;
                    _configs[existingIndex] = config;
                }
                else
                {
                    config.CreatedTime = DateTime.Now;
                    config.LastModifiedTime = DateTime.Now;
                    config.CreatedBy = Environment.UserName;
                    config.LastModifiedBy = Environment.UserName;
                    _configs.Add(config);
                    isNewConfig = true;
                }
            }

            // 保存到文件
            var fileName = $"{config.Id}.json";
            var filePath = Path.Combine(_configDirectory, fileName);
            var json = JsonSerializer.Serialize(config, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);

            // 触发事件
            ConfigChanged?.Invoke(this, new ProtocolParseConfigChangedEventArgs
            {
                ChangeType = isNewConfig ? ConfigChangeType.Added : ConfigChangeType.Updated,
                Config = config
            });

            _logger.LogInformation("配置保存成功: {ConfigName} ({ConfigId})", config.Name, config.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置时发生错误: {ConfigName}", config.Name);
            return false;
        }
    }

    public async Task<bool> DeleteConfigAsync(string configId)
    {
        if (string.IsNullOrEmpty(configId))
            return false;

        try
        {
            ProtocolParseConfig? deletedConfig = null;
            lock (_configLock)
            {
                var index = _configs.FindIndex(c => c.Id == configId);
                if (index >= 0)
                {
                    deletedConfig = _configs[index];
                    _configs.RemoveAt(index);
                }
            }

            if (deletedConfig == null)
            {
                _logger.LogWarning("尝试删除不存在的配置: {ConfigId}", configId);
                return false;
            }

            // 删除文件
            var fileName = $"{configId}.json";
            var filePath = Path.Combine(_configDirectory, fileName);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            // 触发事件
            ConfigChanged?.Invoke(this, new ProtocolParseConfigChangedEventArgs
            {
                ChangeType = ConfigChangeType.Deleted,
                Config = deletedConfig
            });

            _logger.LogInformation("配置删除成功: {ConfigName} ({ConfigId})", deletedConfig.Name, configId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除配置时发生错误: {ConfigId}", configId);
            return false;
        }
    }

    public async Task<ProtocolParseConfig?> CopyConfigAsync(string sourceConfigId, string newConfigName)
    {
        try
        {
            var sourceConfig = await GetConfigByIdAsync(sourceConfigId);
            if (sourceConfig == null)
            {
                _logger.LogWarning("源配置不存在: {ConfigId}", sourceConfigId);
                return null;
            }

            // 检查新名称是否已存在
            if (await IsConfigNameExistsAsync(newConfigName))
            {
                _logger.LogWarning("配置名称已存在: {ConfigName}", newConfigName);
                return null;
            }

            // 创建副本
            var json = JsonSerializer.Serialize(sourceConfig, _jsonOptions);
            var newConfig = JsonSerializer.Deserialize<ProtocolParseConfig>(json, _jsonOptions);
            
            if (newConfig != null)
            {
                newConfig.Id = Guid.NewGuid().ToString();
                newConfig.Name = newConfigName;
                newConfig.Description = $"{sourceConfig.Description} (副本)";
                newConfig.CreatedTime = DateTime.Now;
                newConfig.LastModifiedTime = DateTime.Now;
                newConfig.CreatedBy = Environment.UserName;
                newConfig.LastModifiedBy = Environment.UserName;

                if (await SaveConfigAsync(newConfig))
                {
                    _logger.LogInformation("配置复制成功: {SourceName} -> {NewName}", sourceConfig.Name, newConfigName);
                    return newConfig;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制配置时发生错误: {SourceConfigId} -> {NewConfigName}", sourceConfigId, newConfigName);
            return null;
        }
    }

    #endregion

    #region 私有方法

    private async Task EnsureConfigsLoadedAsync()
    {
        if (_configs.Count == 0)
        {
            await LoadAllConfigsAsync();
        }
    }

    private async Task LoadAllConfigsAsync()
    {
        try
        {
            var configFiles = Directory.GetFiles(_configDirectory, "*.json");
            var loadedConfigs = new List<ProtocolParseConfig>();

            foreach (var filePath in configFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(filePath);
                    var config = JsonSerializer.Deserialize<ProtocolParseConfig>(json, _jsonOptions);
                    if (config != null)
                    {
                        loadedConfigs.Add(config);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载配置文件失败: {FilePath}", filePath);
                }
            }

            lock (_configLock)
            {
                _configs = loadedConfigs;
            }

            _logger.LogInformation("加载了 {Count} 个协议解析配置", loadedConfigs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载协议解析配置时发生错误");
        }
    }

    private async Task LoadAllPhysicalQuantitiesAsync()
    {
        try
        {
            var physicalQuantityFiles = Directory.GetFiles(_physicalQuantityDirectory, "*.json");
            var loadedQuantities = new List<PhysicalQuantity>();

            foreach (var filePath in physicalQuantityFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(filePath);
                    var quantities = JsonSerializer.Deserialize<List<PhysicalQuantity>>(json, _jsonOptions);
                    if (quantities != null)
                    {
                        loadedQuantities.AddRange(quantities);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载物理量文件失败: {FilePath}", filePath);
                }
            }

            lock (_physicalQuantityLock)
            {
                _physicalQuantities = loadedQuantities;
            }

            _logger.LogInformation("加载了 {Count} 个物理量定义", loadedQuantities.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载物理量定义时发生错误");
        }
    }

    #endregion

    #region 配置验证

    public async Task<ValidationResult> ValidateConfigAsync(ProtocolParseConfig config)
    {
        return await Task.FromResult(config.ValidateConfig());
    }

    public async Task<bool> IsConfigNameExistsAsync(string configName, string? excludeConfigId = null)
    {
        if (string.IsNullOrEmpty(configName))
            return false;

        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.Any(c =>
                c.Name.Equals(configName, StringComparison.OrdinalIgnoreCase) &&
                c.Id != excludeConfigId);
        }
    }

    #endregion

    #region 导入导出

    public async Task<bool> ExportConfigToFileAsync(string configId, string filePath)
    {
        try
        {
            var config = await GetConfigByIdAsync(configId);
            if (config == null)
            {
                _logger.LogWarning("导出配置失败，配置不存在: {ConfigId}", configId);
                return false;
            }

            var json = JsonSerializer.Serialize(config, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation("配置导出成功: {ConfigName} -> {FilePath}", config.Name, filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出配置时发生错误: {ConfigId} -> {FilePath}", configId, filePath);
            return false;
        }
    }

    public async Task<ProtocolParseConfig?> ImportConfigFromFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("导入配置失败，文件不存在: {FilePath}", filePath);
                return null;
            }

            var json = await File.ReadAllTextAsync(filePath);
            var config = JsonSerializer.Deserialize<ProtocolParseConfig>(json, _jsonOptions);

            if (config != null)
            {
                // 生成新的ID避免冲突
                config.Id = Guid.NewGuid().ToString();
                config.CreatedTime = DateTime.Now;
                config.LastModifiedTime = DateTime.Now;
                config.CreatedBy = Environment.UserName;
                config.LastModifiedBy = Environment.UserName;

                _logger.LogInformation("配置导入成功: {ConfigName} <- {FilePath}", config.Name, filePath);
            }

            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入配置时发生错误: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> ExportAllConfigsToFileAsync(string filePath)
    {
        try
        {
            var configs = await GetAllConfigsAsync();
            var json = JsonSerializer.Serialize(configs, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation("所有配置导出成功: {Count} 个配置 -> {FilePath}", configs.Count, filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出所有配置时发生错误: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<(int successCount, int failCount, List<string> errors)> ImportConfigsFromFileAsync(string filePath)
    {
        var errors = new List<string>();
        var successCount = 0;
        var failCount = 0;

        try
        {
            if (!File.Exists(filePath))
            {
                errors.Add($"文件不存在: {filePath}");
                return (0, 1, errors);
            }

            var json = await File.ReadAllTextAsync(filePath);
            var configs = JsonSerializer.Deserialize<List<ProtocolParseConfig>>(json, _jsonOptions);

            if (configs == null || configs.Count == 0)
            {
                errors.Add("文件中没有有效的配置数据");
                return (0, 1, errors);
            }

            foreach (var config in configs)
            {
                try
                {
                    // 生成新的ID避免冲突
                    config.Id = Guid.NewGuid().ToString();
                    config.CreatedTime = DateTime.Now;
                    config.LastModifiedTime = DateTime.Now;
                    config.CreatedBy = Environment.UserName;
                    config.LastModifiedBy = Environment.UserName;

                    if (await SaveConfigAsync(config))
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                        errors.Add($"保存配置失败: {config.Name}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    errors.Add($"处理配置 '{config.Name}' 时发生错误: {ex.Message}");
                }
            }

            _logger.LogInformation("批量导入配置完成: 成功 {SuccessCount}, 失败 {FailCount}", successCount, failCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量导入配置时发生错误: {FilePath}", filePath);
            errors.Add($"导入过程发生错误: {ex.Message}");
            failCount++;
        }

        return (successCount, failCount, errors);
    }

    #endregion

    #region 物理量管理

    public async Task<List<PhysicalQuantity>> GetAllPhysicalQuantitiesAsync()
    {
        await EnsurePhysicalQuantitiesLoadedAsync();
        lock (_physicalQuantityLock)
        {
            return _physicalQuantities.ToList();
        }
    }

    public async Task<List<PhysicalQuantity>> GetPhysicalQuantitiesByGroupAsync(string groupName)
    {
        await EnsurePhysicalQuantitiesLoadedAsync();
        lock (_physicalQuantityLock)
        {
            return _physicalQuantities.Where(pq => pq.GroupName == groupName).ToList();
        }
    }

    public async Task<bool> SavePhysicalQuantityAsync(PhysicalQuantity physicalQuantity)
    {
        if (physicalQuantity == null)
        {
            _logger.LogWarning("尝试保存空的物理量对象");
            return false;
        }

        try
        {
            var isNewQuantity = false;
            lock (_physicalQuantityLock)
            {
                var existingIndex = _physicalQuantities.FindIndex(pq => pq.Name == physicalQuantity.Name);
                if (existingIndex >= 0)
                {
                    physicalQuantity.LastModifiedTime = DateTime.Now;
                    _physicalQuantities[existingIndex] = physicalQuantity;
                }
                else
                {
                    physicalQuantity.CreatedTime = DateTime.Now;
                    physicalQuantity.LastModifiedTime = DateTime.Now;
                    _physicalQuantities.Add(physicalQuantity);
                    isNewQuantity = true;
                }
            }

            // 按分组保存到文件
            await SavePhysicalQuantitiesByGroupAsync();

            // 触发事件
            PhysicalQuantityChanged?.Invoke(this, new PhysicalQuantityChangedEventArgs
            {
                ChangeType = isNewQuantity ? ConfigChangeType.Added : ConfigChangeType.Updated,
                PhysicalQuantity = physicalQuantity
            });

            _logger.LogInformation("物理量保存成功: {QuantityName}", physicalQuantity.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存物理量时发生错误: {QuantityName}", physicalQuantity.Name);
            return false;
        }
    }

    public async Task<bool> DeletePhysicalQuantityAsync(string physicalQuantityName)
    {
        if (string.IsNullOrEmpty(physicalQuantityName))
            return false;

        try
        {
            PhysicalQuantity? deletedQuantity = null;
            lock (_physicalQuantityLock)
            {
                var index = _physicalQuantities.FindIndex(pq => pq.Name == physicalQuantityName);
                if (index >= 0)
                {
                    deletedQuantity = _physicalQuantities[index];
                    _physicalQuantities.RemoveAt(index);
                }
            }

            if (deletedQuantity == null)
            {
                _logger.LogWarning("尝试删除不存在的物理量: {QuantityName}", physicalQuantityName);
                return false;
            }

            // 保存更新后的物理量列表
            await SavePhysicalQuantitiesByGroupAsync();

            // 触发事件
            PhysicalQuantityChanged?.Invoke(this, new PhysicalQuantityChangedEventArgs
            {
                ChangeType = ConfigChangeType.Deleted,
                PhysicalQuantity = deletedQuantity
            });

            _logger.LogInformation("物理量删除成功: {QuantityName}", physicalQuantityName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除物理量时发生错误: {QuantityName}", physicalQuantityName);
            return false;
        }
    }

    public async Task<List<PhysicalQuantityGroup>> GetPhysicalQuantityGroupsAsync()
    {
        await EnsurePhysicalQuantitiesLoadedAsync();
        lock (_physicalQuantityLock)
        {
            return _physicalQuantities
                .GroupBy(pq => pq.GroupName)
                .Select(g => new PhysicalQuantityGroup
                {
                    Name = g.Key,
                    DisplayName = g.Key,
                    PhysicalQuantities = g.OrderBy(pq => pq.SortOrder).ThenBy(pq => pq.Name).ToList(),
                    SortOrder = g.Min(pq => pq.SortOrder),
                    IsEnabled = g.Any(pq => pq.IsEnabled)
                })
                .OrderBy(g => g.SortOrder)
                .ThenBy(g => g.Name)
                .ToList();
        }
    }

    #endregion

    #region 配置应用

    public async Task<List<ProtocolParseConfig>> GetActiveConfigsAsync(DataPacketProtocolType protocolType)
    {
        await EnsureConfigsLoadedAsync();
        lock (_configLock)
        {
            return _configs.Where(c => c.ProtocolType == protocolType && c.IsEnabled).ToList();
        }
    }

    public async Task<bool> SetConfigActiveStatusAsync(string configId, bool isActive)
    {
        try
        {
            var config = await GetConfigByIdAsync(configId);
            if (config == null)
            {
                _logger.LogWarning("设置配置激活状态失败，配置不存在: {ConfigId}", configId);
                return false;
            }

            if (config.IsEnabled == isActive)
            {
                return true; // 状态未变化
            }

            config.IsEnabled = isActive;
            config.LastModifiedTime = DateTime.Now;
            config.LastModifiedBy = Environment.UserName;

            var success = await SaveConfigAsync(config);
            if (success)
            {
                // 触发激活状态变更事件
                ConfigChanged?.Invoke(this, new ProtocolParseConfigChangedEventArgs
                {
                    ChangeType = ConfigChangeType.ActiveStatusChanged,
                    Config = config
                });

                _logger.LogInformation("配置激活状态已更新: {ConfigName} -> {IsActive}", config.Name, isActive);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置配置激活状态时发生错误: {ConfigId} -> {IsActive}", configId, isActive);
            return false;
        }
    }

    #endregion

    #region 私有方法扩展

    private async Task EnsurePhysicalQuantitiesLoadedAsync()
    {
        if (_physicalQuantities.Count == 0)
        {
            await LoadAllPhysicalQuantitiesAsync();
        }
    }

    private async Task SavePhysicalQuantitiesByGroupAsync()
    {
        try
        {
            var groups = _physicalQuantities.GroupBy(pq => pq.GroupName);

            foreach (var group in groups)
            {
                var fileName = $"{group.Key}.json";
                var filePath = Path.Combine(_physicalQuantityDirectory, fileName);
                var json = JsonSerializer.Serialize(group.ToList(), _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存物理量分组文件时发生错误");
        }
    }

    #endregion

    #region IDisposable

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogInformation("协议数据解析配置服务已释放");
        }
    }

    #endregion
}
