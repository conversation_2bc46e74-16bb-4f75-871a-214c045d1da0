using Microsoft.Extensions.Logging;
using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 数据桥接服务实现
/// 负责连接串口服务和数据分包器服务，将串口接收的数据传递给分包器进行处理
/// </summary>
public class DataBridgeService : IDataBridgeService, IDisposable
{
    #region 字段

    private readonly ILogger<DataBridgeService> _logger;
    private readonly ISerialPortService _serialPortService;
    private readonly IDataPacketService _dataPacketService;
    private bool _isStarted = false;
    private bool _disposed = false;
    private long _totalBytesProcessed = 0;
    private long _totalPacketsForwarded = 0;

    #endregion

    #region 属性

    public bool IsStarted => _isStarted;

    #endregion

    #region 构造函数

    public DataBridgeService(
        ILogger<DataBridgeService> logger,
        ISerialPortService serialPortService,
        IDataPacketService dataPacketService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _dataPacketService = dataPacketService ?? throw new ArgumentNullException(nameof(dataPacketService));

        _logger.LogDebug("数据桥接服务已初始化");
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 启动数据桥接服务
    /// </summary>
    public void Start()
    {
        if (_isStarted)
        {
            _logger.LogWarning("数据桥接服务已经启动");
            return;
        }

        try
        {
            // 订阅串口数据接收事件
            _serialPortService.DataReceived += OnSerialPortDataReceived;

            _isStarted = true;
            _logger.LogInformation("数据桥接服务已启动");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动数据桥接服务失败");
            throw;
        }
    }

    /// <summary>
    /// 停止数据桥接服务
    /// </summary>
    public void Stop()
    {
        if (!_isStarted)
        {
            _logger.LogWarning("数据桥接服务未启动");
            return;
        }

        try
        {
            // 取消订阅串口数据接收事件
            _serialPortService.DataReceived -= OnSerialPortDataReceived;

            _isStarted = false;
            _logger.LogInformation("数据桥接服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止数据桥接服务失败");
            throw;
        }
    }

    /// <summary>
    /// 获取桥接统计信息
    /// </summary>
    public Dictionary<string, object> GetStatistics()
    {
        return new Dictionary<string, object>
        {
            ["IsStarted"] = _isStarted,
            ["TotalBytesProcessed"] = _totalBytesProcessed,
            ["TotalPacketsForwarded"] = _totalPacketsForwarded,
            ["SerialPortConnected"] = _serialPortService.IsConnected,
            ["SerialPortStatus"] = _serialPortService.ConnectionStatus.ToString(),
            ["DataPacketServiceStats"] = _dataPacketService.GetStatistics()
        };
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 串口数据接收事件处理
    /// </summary>
    private async void OnSerialPortDataReceived(object? sender, SerialPortDataReceivedEventArgs e)
    {
        if (_disposed || !_isStarted)
            return;

        try
        {
            _logger.LogDebug("接收到串口数据: {Length} 字节, 端口: {PortName}", 
                e.Data.Length, e.PortName);

            // 更新统计信息
            Interlocked.Add(ref _totalBytesProcessed, e.Data.Length);

            // 将数据传递给数据分包器服务进行处理
            var results = await _dataPacketService.ProcessDataAsync(e.Data, e.PortName);

            // 更新转发统计
            Interlocked.Add(ref _totalPacketsForwarded, results.Count(r => r.IsSuccess));

            _logger.LogTrace("数据桥接完成: 处理 {ByteCount} 字节, 解析出 {PacketCount} 个数据包", 
                e.Data.Length, results.Count(r => r.IsSuccess));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理串口数据桥接时发生错误");
        }
    }

    #endregion

    #region IDisposable 实现

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            Stop();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放数据桥接服务资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }

    #endregion
}
