using System.Windows;
using AirMonitor.ViewModels;

namespace AirMonitor.Views;

/// <summary>
/// 串口连接对话框
/// </summary>
public partial class SerialPortConnectionDialog : Window
{
    public SerialPortConnectionDialog()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 如果DataContext是SerialPortConnectionViewModel，则初始化它
        if (DataContext is SerialPortConnectionViewModel viewModel)
        {
            await viewModel.InitializeAsync();
            
            // 订阅DialogResult变化
            viewModel.PropertyChanged += (s, args) =>
            {
                if (args.PropertyName == nameof(viewModel.DialogResult) && viewModel.DialogResult.HasValue)
                {
                    DialogResult = viewModel.DialogResult;
                }
            };
        }
    }
}
