{"id": "example-module-001", "name": "模块机通讯协议示例配置", "description": "这是一个模块机通讯协议的配置示例，展示了基于索引(idx)的复杂数据映射", "protocolType": "ModuleProtocol", "commandCode": 85, "physicalQuantities": [{"name": "IndexValue", "displayName": "索引值", "description": "数据帧中的索引值", "dataType": "UInt16", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 65535.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "索引信息", "sortOrder": 1, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "用于标识数据类型的索引值"}, {"name": "IndexHighByte", "displayName": "索引高字节", "description": "索引值的高8位", "dataType": "UInt8", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 255.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "索引信息", "sortOrder": 2, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "索引值高8位，用于分类"}, {"name": "IndexLowByte", "displayName": "索引低字节", "description": "索引值的低8位", "dataType": "UInt8", "unit": "", "scaleFactor": 1.0, "offset": 0.0, "minValue": 0.0, "maxValue": 255.0, "decimalPlaces": 0, "isReadOnly": true, "groupName": "索引信息", "sortOrder": 3, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "索引值低8位，用于具体标识"}, {"name": "TemperatureValue", "displayName": "温度值", "description": "当索引为0x0001时的温度数据", "dataType": "Int16", "unit": "°C", "scaleFactor": 0.1, "offset": 0.0, "minValue": -50.0, "maxValue": 100.0, "decimalPlaces": 1, "isReadOnly": true, "groupName": "传感器数据", "sortOrder": 10, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "温度传感器数据，索引0x0001"}, {"name": "PressureValue", "displayName": "压力值", "description": "当索引为0x0002时的压力数据", "dataType": "UInt16", "unit": "kPa", "scaleFactor": 0.01, "offset": 0.0, "minValue": 0.0, "maxValue": 1000.0, "decimalPlaces": 2, "isReadOnly": true, "groupName": "传感器数据", "sortOrder": 11, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "压力传感器数据，索引0x0002"}, {"name": "VoltageValue", "displayName": "电压值", "description": "当索引为0x0003时的电压数据", "dataType": "UInt16", "unit": "V", "scaleFactor": 0.1, "offset": 0.0, "minValue": 0.0, "maxValue": 500.0, "decimalPlaces": 1, "isReadOnly": true, "groupName": "电气参数", "sortOrder": 20, "isEnabled": true, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "remarks": "电压传感器数据，索引0x0003"}], "dataMappings": [{"id": "idx-mapping-001", "name": "温度数据索引映射", "description": "当索引值为0x0001时，将数据字节映射到温度值", "mappingType": "IndexMapping", "physicalQuantityName": "TemperatureValue", "byteRange": null, "bitPositions": [], "indexMapping": {"indexValue": 1, "dataByteRange": {"startIndex": 4, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "conditionExpression": null}, "isEnabled": true, "sortOrder": 1, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "idx-mapping-002", "name": "压力数据索引映射", "description": "当索引值为0x0002时，将数据字节映射到压力值", "mappingType": "IndexMapping", "physicalQuantityName": "PressureValue", "byteRange": null, "bitPositions": [], "indexMapping": {"indexValue": 2, "dataByteRange": {"startIndex": 4, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "conditionExpression": null}, "isEnabled": true, "sortOrder": 2, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}, {"id": "idx-mapping-003", "name": "电压数据索引映射", "description": "当索引值为0x0003时，将数据字节映射到电压值", "mappingType": "IndexMapping", "physicalQuantityName": "VoltageValue", "byteRange": null, "bitPositions": [], "indexMapping": {"indexValue": 3, "dataByteRange": {"startIndex": 4, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "conditionExpression": null}, "isEnabled": true, "sortOrder": 3, "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z"}], "indexParseConfig": {"indexByteRange": {"startIndex": 0, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "indexCountByteRange": {"startIndex": 2, "length": 2, "byteOrder": "Little<PERSON><PERSON><PERSON>"}, "parseIndexAsPhysicalQuantity": true, "indexHighBytePhysicalQuantityName": "IndexHighByte", "indexLowBytePhysicalQuantityName": "IndexLowByte", "indexBitParseConfigs": []}, "isEnabled": true, "version": "1.0.0", "createdTime": "2024-01-01T00:00:00Z", "lastModifiedTime": "2024-01-01T00:00:00Z", "createdBy": "System", "lastModifiedBy": "System"}