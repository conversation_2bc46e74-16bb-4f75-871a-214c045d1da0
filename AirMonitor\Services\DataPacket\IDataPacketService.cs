using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 数据分包器服务接口
/// </summary>
public interface IDataPacketService
{
    /// <summary>
    /// 数据包解析完成事件
    /// </summary>
    event EventHandler<DataPacketParsedEventArgs>? PacketParsed;

    /// <summary>
    /// 数据包解析错误事件
    /// </summary>
    event EventHandler<DataPacketParseErrorEventArgs>? ParseError;

    /// <summary>
    /// 当前协议类型
    /// </summary>
    DataPacketProtocolType ProtocolType { get; }

    /// <summary>
    /// 是否启用协议自动识别
    /// </summary>
    bool AutoDetectProtocol { get; set; }

    /// <summary>
    /// 缓冲区大小（字节）
    /// </summary>
    int BufferSize { get; }

    /// <summary>
    /// 当前缓冲区中的数据长度
    /// </summary>
    int BufferedDataLength { get; }

    /// <summary>
    /// 已解析的数据包总数
    /// </summary>
    long TotalParsedPackets { get; }

    /// <summary>
    /// 解析错误总数
    /// </summary>
    long TotalParseErrors { get; }

    /// <summary>
    /// 设置协议类型
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    void SetProtocolType(DataPacketProtocolType protocolType);

    /// <summary>
    /// 处理接收到的数据
    /// </summary>
    /// <param name="data">接收到的数据</param>
    /// <param name="portName">源端口名称</param>
    /// <returns>解析结果列表</returns>
    Task<List<DataPacketParseResult>> ProcessDataAsync(byte[] data, string portName = "");

    /// <summary>
    /// 处理单个数据字节
    /// </summary>
    /// <param name="dataByte">数据字节</param>
    /// <param name="portName">源端口名称</param>
    /// <returns>解析结果，如果没有完整数据包则返回null</returns>
    Task<DataPacketParseResult?> ProcessByteAsync(byte dataByte, string portName = "");

    /// <summary>
    /// 清空缓冲区
    /// </summary>
    void ClearBuffer();

    /// <summary>
    /// 获取缓冲区状态信息
    /// </summary>
    /// <returns>缓冲区状态信息</returns>
    string GetBufferStatus();

    /// <summary>
    /// 验证数据包的CRC校验
    /// </summary>
    /// <param name="packet">要验证的数据包</param>
    /// <returns>校验是否通过</returns>
    bool ValidatePacketCrc(DataPacketBase packet);

    /// <summary>
    /// 尝试从原始数据解析数据包
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <param name="protocolType">协议类型</param>
    /// <returns>解析结果</returns>
    DataPacketParseResult TryParsePacket(byte[] rawData, DataPacketProtocolType protocolType);

    /// <summary>
    /// 检测数据的协议类型
    /// </summary>
    /// <param name="data">要检测的数据</param>
    /// <returns>检测到的协议类型，如果无法确定则返回null</returns>
    DataPacketProtocolType? DetectProtocolType(byte[] data);

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息字典</returns>
    Dictionary<string, object> GetStatistics();

    /// <summary>
    /// 重置统计信息
    /// </summary>
    void ResetStatistics();
}
