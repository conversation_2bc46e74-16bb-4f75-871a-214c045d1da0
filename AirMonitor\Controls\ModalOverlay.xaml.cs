using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace AirMonitor.Controls;

/// <summary>
/// 模态蒙版控件，用于在对话框显示时提供半透明覆盖层
/// </summary>
public partial class ModalOverlay : UserControl
{
    #region 依赖属性

    /// <summary>
    /// 蒙版画刷依赖属性
    /// </summary>
    public static readonly DependencyProperty OverlayBrushProperty =
        DependencyProperty.Register(
            nameof(OverlayBrush),
            typeof(Brush),
            typeof(ModalOverlay),
            new PropertyMetadata(new SolidColorBrush(Color.FromArgb(128, 0, 0, 0))));

    /// <summary>
    /// 对话框内容依赖属性
    /// </summary>
    public static readonly DependencyProperty DialogContentProperty =
        DependencyProperty.Register(
            nameof(DialogContent),
            typeof(object),
            typeof(ModalOverlay),
            new PropertyMetadata(null));

    /// <summary>
    /// 是否启用动画依赖属性
    /// </summary>
    public static readonly DependencyProperty IsAnimationEnabledProperty =
        DependencyProperty.Register(
            nameof(IsAnimationEnabled),
            typeof(bool),
            typeof(ModalOverlay),
            new PropertyMetadata(true));

    #endregion

    #region 属性

    /// <summary>
    /// 蒙版画刷
    /// </summary>
    public Brush OverlayBrush
    {
        get => (Brush)GetValue(OverlayBrushProperty);
        set => SetValue(OverlayBrushProperty, value);
    }

    /// <summary>
    /// 对话框内容
    /// </summary>
    public object DialogContent
    {
        get => GetValue(DialogContentProperty);
        set => SetValue(DialogContentProperty, value);
    }

    /// <summary>
    /// 是否启用动画
    /// </summary>
    public bool IsAnimationEnabled
    {
        get => (bool)GetValue(IsAnimationEnabledProperty);
        set => SetValue(IsAnimationEnabledProperty, value);
    }

    #endregion

    #region 事件

    /// <summary>
    /// 淡入动画完成事件
    /// </summary>
    public event EventHandler? FadeInCompleted;

    /// <summary>
    /// 淡出动画完成事件
    /// </summary>
    public event EventHandler? FadeOutCompleted;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 ModalOverlay 类的新实例
    /// </summary>
    public ModalOverlay()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 控件加载完成事件处理
    /// </summary>
    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        // 绑定动画完成事件
        if (Resources["FadeInStoryboard"] is Storyboard fadeInStoryboard)
        {
            fadeInStoryboard.Completed += (s, args) => FadeInCompleted?.Invoke(this, EventArgs.Empty);
        }

        if (Resources["FadeOutStoryboard"] is Storyboard fadeOutStoryboard)
        {
            fadeOutStoryboard.Completed += (s, args) => FadeOutCompleted?.Invoke(this, EventArgs.Empty);
        }
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 显示蒙版（淡入效果）
    /// </summary>
    public void Show()
    {
        Visibility = Visibility.Visible;

        if (IsAnimationEnabled && Resources["FadeInStoryboard"] is Storyboard fadeInStoryboard)
        {
            fadeInStoryboard.Begin();
        }
        else
        {
            OverlayBorder.Opacity = 1;
            FadeInCompleted?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// 隐藏蒙版（淡出效果）
    /// </summary>
    public void Hide()
    {
        if (IsAnimationEnabled && Resources["FadeOutStoryboard"] is Storyboard fadeOutStoryboard)
        {
            fadeOutStoryboard.Completed += (s, e) => Visibility = Visibility.Collapsed;
            fadeOutStoryboard.Begin();
        }
        else
        {
            OverlayBorder.Opacity = 0;
            Visibility = Visibility.Collapsed;
            FadeOutCompleted?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// 立即显示蒙版（无动画）
    /// </summary>
    public void ShowImmediate()
    {
        Visibility = Visibility.Visible;
        OverlayBorder.Opacity = 1;
    }

    /// <summary>
    /// 立即隐藏蒙版（无动画）
    /// </summary>
    public void HideImmediate()
    {
        OverlayBorder.Opacity = 0;
        Visibility = Visibility.Collapsed;
    }

    #endregion
}
